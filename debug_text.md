[SFCCanvas] 已订阅步骤位置变化: af7750ea-33f8-4ee0-be90-742bcfbb620e
[SFCCanvas] 已订阅ViewModel位置变化事件 - 步骤:1, 转换:0, 分支:0
[SFCStepView] 拖动更新位置: af7750ea-33f8-4ee0-be90-742bcfbb620e -> 200,200
[SFCCanvas] 位置变化: SFCStepViewModel.IsSelected
“PC_Control2.Demo.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.Serialization.Formatters.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“PC_Control2.Demo.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.Intrinsics.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
[GetActualConnectPointPosition] 步骤连接点: 索引0, 位置250,322
[CalculateConnectPointAlignedPositionDynamic] 动态对齐计算: 连接点250,322, 目标位置213,342
[AddTransitionToCollections] Model位置: (213, 342)
[AddTransitionToCollections] ViewModel位置: (213, 342)
[SFCCanvas] 为新创建的转换条件添加位置变化监听: 76f7cbaf-58a2-4b4c-ac8a-8b68ff363c2d
[连接创建] 准备创建连接: af7750ea-33f8-4ee0-be90-742bcfbb620e -> 76f7cbaf-58a2-4b4c-ac8a-8b68ff363c2d
[AddConnection] 开始执行: af7750ea-33f8-4ee0-be90-742bcfbb620e -> 76f7cbaf-58a2-4b4c-ac8a-8b68ff363c2d
[AddConnection] 对象查找结果: sourceObject=SFCStepModel, targetObject=SFCTransitionModel
[AddConnection] 位置获取: 源ViewModel位置=200,200, 源Model位置=200,200
[AddConnection] 位置获取: 目标ViewModel位置=213,342, 目标Model位置=213,342
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[AddConnection] 创建连接: af7750ea-33f8-4ee0-be90-742bcfbb620e -> 76f7cbaf-58a2-4b4c-ac8a-8b68ff363c2d
[AddConnection] 源位置: 200,200, 目标位置: 213,342
[AddConnection] 源对象类型: SFCStepModel, 目标对象类型: SFCTransitionModel
[AddConnection] 源连接点: 250,322, 目标连接点: 273.3,342
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 728f84b7-9e33-4d3c-a7c1-c2cd3772a18f 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 250,322, 终点: 273.3,342
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, IsInput: False, ConnectionId: 728f84b7-9e33-4d3c-a7c1-c2cd3772a18f
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCTransitionViewModel, IsInput: True, ConnectionId: 728f84b7-9e33-4d3c-a7c1-c2cd3772a18f
[UpdateConnectPointStates] 连接点状态更新完成: af7750ea-33f8-4ee0-be90-742bcfbb620e[0] -> 76f7cbaf-58a2-4b4c-ac8a-8b68ff363c2d[0]
[CreateConnectionPath] 连接 728f84b7-9e33-4d3c-a7c1-c2cd3772a18f 连接点不重叠，创建连接线
[CreateConnectionPath] 起点: (250.0, 322.0), 终点: (273.3, 342.0), 距离: 30.7px
[贝塞尔曲线创建] 起点: (250.0, 322.0), 终点: (273.3, 342.0)
[贝塞尔曲线创建] Y差异: 20.0px, X差异: 23.3px, 控制点偏移: 10.0px
[贝塞尔曲线创建] PathPoints[0]: (250.0, 322.0), PathPoints[1]: (273.3, 342.0)
添加连接线: 728f84b7-9e33-4d3c-a7c1-c2cd3772a18f
[AddConnection] 延迟更新连接线 728f84b7-9e33-4d3c-a7c1-c2cd3772a18f 的路径点
[PathPoints集合变化] 连接 728f84b7-9e33-4d3c-a7c1-c2cd3772a18f 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 728f84b7-9e33-4d3c-a7c1-c2cd3772a18f 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 728f84b7-9e33-4d3c-a7c1-c2cd3772a18f 的PathPoints已更新，重新评估连接线
[AddConnection] 延迟更新 - 源对象类型: SFCStepViewModel, 目标对象类型: SFCTransitionViewModel
[AddConnection] 延迟更新 - 源位置: 200,200, 目标位置: 213,342
[AddConnection] 延迟更新后的路径点: 250,322 -> 273.3,342
[AddConnection] PathPoints集合已更新，应该触发重新评估
[SFCTransitionView] 拖动更新位置: 76f7cbaf-58a2-4b4c-ac8a-8b68ff363c2d -> 213,342
[SFCCanvas] 位置变化: SFCTransitionViewModel.IsSelected
[GetActualConnectPointPosition] 转换条件连接点: 索引0, 位置273.3,372
[InsertEndAfterSelected] 动态计算结尾步骤位置: 连接点273.3,372, 步骤位置223.3,452
[AddConnection] 开始执行: 76f7cbaf-58a2-4b4c-ac8a-8b68ff363c2d -> 6db97ce1-dd52-44a6-8d2e-d699d95951d6
[AddConnection] 对象查找结果: sourceObject=SFCTransitionModel, targetObject=null
[AddConnection] ❌ 对象查找失败
[SFCCanvas] 为新创建的步骤添加位置变化监听: 6db97ce1-dd52-44a6-8d2e-d699d95951d6