using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using PC_Control2.Demo.ViewModels;

namespace PC_Control2.Demo.Controls
{
    /// <summary>
    /// SFC顺控器终止控件 - 专用于显示SFC流程的终止元素
    /// </summary>
    public partial class SFCTerminatorView : UserControl
    {
        #region 构造函数

        public SFCTerminatorView()
        {
            InitializeComponent();
            
            // 设置数据上下文变化事件
            DataContextChanged += OnDataContextChanged;
            
            // 设置鼠标事件
            MouseLeftButtonDown += OnMouseLeftButtonDown;
            MouseEnter += OnMouseEnter;
            MouseLeave += OnMouseLeave;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 数据上下文变化处理
        /// </summary>
        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (e.NewValue is SFCStepViewModel stepViewModel)
            {
                // 绑定选中状态变化事件
                stepViewModel.PropertyChanged += (s, args) =>
                {
                    if (args.PropertyName == nameof(SFCStepViewModel.IsSelected))
                    {
                        UpdateVisualState();
                    }
                };
            }
        }

        /// <summary>
        /// 鼠标左键按下处理
        /// </summary>
        private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (DataContext is SFCStepViewModel stepViewModel)
            {
                // 选中当前元素
                stepViewModel.IsSelected = true;
                
                // 标记事件已处理，防止冒泡
                e.Handled = true;
            }
        }

        /// <summary>
        /// 鼠标进入处理
        /// </summary>
        private void OnMouseEnter(object sender, MouseEventArgs e)
        {
            // 鼠标悬停效果由样式触发器处理
            Cursor = Cursors.Hand;
        }

        /// <summary>
        /// 鼠标离开处理
        /// </summary>
        private void OnMouseLeave(object sender, MouseEventArgs e)
        {
            Cursor = Cursors.Arrow;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新视觉状态
        /// </summary>
        private void UpdateVisualState()
        {
            if (DataContext is SFCStepViewModel stepViewModel)
            {
                // 选中状态的视觉效果由XAML中的DataTrigger处理
                // 这里可以添加额外的状态更新逻辑
            }
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取顶部连接点的位置
        /// </summary>
        public Point GetTopConnectPointPosition()
        {
            // 返回顶部连接点的中心位置
            return new Point(25, 10); // Canvas.Left="20" + Width/2, Canvas.Top="5" + Height/2
        }

        /// <summary>
        /// 获取元素的边界矩形
        /// </summary>
        public Rect GetBounds()
        {
            return new Rect(0, 0, Width, Height);
        }

        #endregion
    }
}
