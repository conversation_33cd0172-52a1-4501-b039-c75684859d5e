# 顺控器终止连接线功能测试指南

## 测试目标
验证PC_Control2项目中SFC编辑器的顺控器终止元素连接线功能是否正常工作。

## 测试环境
- 项目：PC_Control2.Demo
- 编辑器：SFC编辑器（EnhancedSFCEditorView）
- 测试元素：转换条件 → 顺控器终止

## 测试步骤

### 1. 启动应用程序
```bash
# 在项目根目录执行
dotnet run --project PC_Control2.Demo.csproj
# 或者运行
run_zh.bat
```

### 2. 打开SFC编辑器
- 启动应用程序后，切换到SFC编辑器界面
- 确认工具栏中有"顺控器终止"按钮

### 3. 创建测试场景
1. **添加初始步骤**：
   - 点击"插入步"按钮，创建第一个步骤（S1）
   
2. **添加转换条件**：
   - 选中步骤S1
   - 点击"转移条件"按钮，在S1下方创建转换条件（T1）
   
3. **添加顺控器终止**：
   - 选中转换条件T1
   - 点击"顺控器终止"按钮，在T1下方创建顺控器终止元素

### 4. 验证连接线功能

#### 4.1 静态连接线验证
- **预期结果**：T1转换条件的底部输出连接点与顺控器终止元素的顶部输入连接点之间应该有一条连接线
- **验证要点**：
  - 连接线两端精确对接连接点中心
  - 连接线样式与其他SFC元素连接线一致（颜色、粗细）
  - 连接线为直线或贝塞尔曲线（根据距离自动选择）

#### 4.2 动态更新验证
1. **拖拽转换条件T1**：
   - 选中T1，拖拽到不同位置
   - **预期结果**：连接线实时跟随更新，始终连接两个连接点
   
2. **拖拽顺控器终止元素**：
   - 选中顺控器终止元素，拖拽到不同位置
   - **预期结果**：连接线实时跟随更新，始终连接两个连接点

#### 4.3 选中状态验证
1. **选中连接线**：
   - 点击连接线
   - **预期结果**：连接线高亮显示（通常变为蓝色并有发光效果）
   
2. **选中元素时连接线状态**：
   - 选中T1或顺控器终止元素
   - **预期结果**：相关连接线可能有相应的视觉反馈

#### 4.4 连接点重叠验证
1. **将顺控器终止元素拖拽到T1正下方**：
   - 使两个连接点重叠或非常接近
   - **预期结果**：连接线应该隐藏，只显示重叠的连接点

## 技术验证要点

### 连接点坐标精确性
- **转换条件输出连接点**：应位于转换条件底部中心
- **顺控器终止输入连接点**：应位于顺控器终止元素顶部（Canvas.Left="20" Canvas.Top="6"的中心）

### 连接线样式一致性
- 与"转换条件→普通步骤"的连接线样式完全一致
- 支持选中高亮效果
- 支持动态显示/隐藏机制

### 性能验证
- 拖拽时连接线更新应该流畅，无明显延迟
- 多个连接线同时存在时性能正常

## 预期问题排查

### 问题1：连接线不显示
- **可能原因**：连接创建失败
- **检查方法**：查看控制台输出，确认AddConnection方法是否被调用

### 问题2：连接线位置偏移
- **可能原因**：连接点坐标计算错误
- **检查方法**：对比顺控器终止元素与普通步骤的连接线位置

### 问题3：拖拽时连接线不更新
- **可能原因**：位置变化监听机制问题
- **检查方法**：测试普通步骤的连接线是否正常更新

## 测试结果记录

### 功能完整性
- [ ] 连接线正确显示
- [ ] 连接点精确对接
- [ ] 动态更新正常
- [ ] 选中状态正常
- [ ] 重叠隐藏正常

### 视觉一致性
- [ ] 连接线样式一致
- [ ] 颜色和粗细正确
- [ ] 动画效果正常

### 性能表现
- [ ] 拖拽流畅
- [ ] 无明显延迟
- [ ] 内存使用正常

## 修复记录

### 已完成的修改
1. **连接点坐标计算**：在`CalculateElementConnectPoint`方法中添加了顺控器终止元素的特殊处理
2. **元素识别**：通过`Description = "TERMINATOR_ELEMENT"`标识区分顺控器终止元素
3. **坐标精确性**：使用正确的连接点坐标`(elementPosition.X + 20 + 5, elementPosition.Y + 6 + 5)`
4. **时序问题修复**：修复了`InsertEndAfterSelected`方法中的执行顺序问题，确保在创建连接前步骤已添加到集合中

### 关键问题修复：时序问题
**问题描述**：在创建连接时，`AddConnection`方法找不到目标对象（顺控器终止元素）
**根本原因**：`AddConnection`调用时，目标步骤还没有被添加到`Steps`集合中
**解决方案**：调整执行顺序，将`AddStepToCollections(endStep)`移到连接创建之前
**修改位置**：`ViewModels\EnhancedSFCViewModel.cs` 第2658-2698行

### 技术实现要点
- 顺控器终止元素输入连接点坐标：基于SFCTerminatorView.xaml中的Canvas.Left="20" Canvas.Top="6"
- 连接点中心偏移：+5像素（连接点控件尺寸10x10的一半）
- 兼容性保持：不影响现有普通步骤元素的连接功能
