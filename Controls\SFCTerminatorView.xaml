<UserControl
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="clr-namespace:PC_Control2.Demo.ViewModels"
             xmlns:controls="clr-namespace:PC_Control2.Demo.Controls"
             xmlns:models="clr-namespace:PC_Control2.Demo.Models"
             xmlns:config="clr-namespace:PC_Control2.Demo.Configuration"
             xmlns:av="http://schemas.microsoft.com/expression/blend/2008" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="av" x:Class="PC_Control2.Demo.Controls.SFCTerminatorView"
             Width="50"
             Height="50">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/SFCBlueprintStyles.xaml"/>
                <ResourceDictionary Source="../Styles/Controls/SFCTerminatorStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 布尔值到可见性转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- 主画布容器 -->
    <Canvas x:Name="MainCanvas" 
            Background="Transparent"
            ClipToBounds="False">

        <!-- 右键菜单 -->
        <Canvas.ContextMenu>
            <ContextMenu Style="{StaticResource BlueprintContextMenuStyle}">
                <MenuItem Header="编辑终止元素"
                         Command="{Binding EditStepCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="⚙"/>
                <Separator/>
                <MenuItem Header="删除"
                         Command="{Binding DeleteCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="🗑"/>
            </ContextMenu>
        </Canvas.ContextMenu>

        <!-- 顶部连接点 - 输入连接点 -->
        <controls:SFCConnectPoint x:Name="TopConnectPoint"
                 Canvas.Left="20" Canvas.Top="6"
                 HorizontalAlignment="Left" VerticalAlignment="Center"
                 PointType="Input" Index="0" ElementId="{Binding Id}" ElementType="Step">
            <controls:SFCConnectPoint.Style>
                <Style TargetType="{x:Type controls:SFCConnectPoint}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </controls:SFCConnectPoint.Style>
        </controls:SFCConnectPoint>

        <!-- 垂直线段 - 从连接点向下延伸 -->
        <Rectangle x:Name="VerticalLine"
                   Canvas.Left="23.5" Canvas.Top="15"
                   Width="3" Height="20"
                   Fill="#FF888888" HorizontalAlignment="Left" VerticalAlignment="Center">
            <Rectangle.Style>
                <Style TargetType="{x:Type Rectangle}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Fill" Value="#FF4A90E2"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Rectangle.Style>
        </Rectangle>

        <!-- 水平横线 - 位于垂直线段底部 -->
        <Rectangle x:Name="HorizontalLine"
                   Canvas.Left="18.5" Canvas.Top="35"
                   Width="13" Height="4.5"
                   Fill="#FF888888">
            <Rectangle.Style>
                <Style TargetType="{x:Type Rectangle}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Fill" Value="#FF4A90E2"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Rectangle.Style>
        </Rectangle>

        <!-- 标签文本 - 显示"顺控器终止" -->
        <TextBlock x:Name="TerminatorLabel" Canvas.Top="40"
                   Width="50"
                   Text="顺控器终止"
                   FontSize="8"
                   FontWeight="SemiBold"
                   Foreground="#FF888888"
                   HorizontalAlignment="Left"
                   TextAlignment="Center"
                   TextTrimming="CharacterEllipsis" VerticalAlignment="Center">
            <TextBlock.Style>
                <Style TargetType="{x:Type TextBlock}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Foreground" Value="#FF4A90E2"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </TextBlock.Style>
        </TextBlock>

    </Canvas>
</UserControl>