using CommunityToolkit.Mvvm.Input;
using PC_Control2.Demo.Models;
using PC_Control2.Demo.Services;
using PC_Control2.Demo.Views;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace PC_Control2.Demo.ViewModels;

/// <summary>
/// 增强版SFC编辑器ViewModel - 基于IEC 61131-3标准的专业SFC编辑器
/// </summary>
public class EnhancedSFCViewModel : ViewModelBase
{
    private readonly SFCValidator _validator;
    private readonly SFCCodeGenerator _codeGenerator;
    private readonly SFCDebuggerService _debugger;
    private readonly SFCCompilerIntegrationService _compilerIntegration;
    
    private SFCModel _currentSFC = new();
    private object? _selectedElement;
    private bool _isConnecting;
    private object? _connectionSource;
    private SFCValidationResult? _validationResult;
    private string _generatedCode = "";
    private bool _isModified;
    private string _statusMessage = "就绪";

    public SFCModel CurrentSFC
    {
        get => _currentSFC;
        set => SetProperty(ref _currentSFC, value);
    }

    public object? SelectedElement
    {
        get => _selectedElement;
        set
        {
            if (SetProperty(ref _selectedElement, value))
            {
                // 当选中元素改变时，更新命令的CanExecute状态
                OnSelectedElementChanged();
            }
        }
    }

    public bool IsConnecting
    {
        get => _isConnecting;
        set => SetProperty(ref _isConnecting, value);
    }

    public object? ConnectionSource
    {
        get => _connectionSource;
        set => SetProperty(ref _connectionSource, value);
    }

    public SFCValidationResult? ValidationResult
    {
        get => _validationResult;
        set => SetProperty(ref _validationResult, value);
    }

    public string GeneratedCode
    {
        get => _generatedCode;
        set => SetProperty(ref _generatedCode, value);
    }

    public bool IsModified
    {
        get => _isModified;
        set => SetProperty(ref _isModified, value);
    }

    public new string StatusMessage
    {
        get => _statusMessage;
        set => SetProperty(ref _statusMessage, value);
    }

    // 数据模型集合
    public ObservableCollection<SFCStepModel> Steps { get; } = new();
    public ObservableCollection<SFCTransitionModel> Transitions { get; } = new();
    public ObservableCollection<SFCBranchModel> Branches { get; } = new();
    public ObservableCollection<SFCConnectionModel> Connections { get; } = new();
    public ObservableCollection<SFCActionModel> Actions { get; } = new();

    // 兼容性ViewModel集合（用于现有UI）
    public ObservableCollection<SFCStepViewModel> StepViewModels { get; } = new();
    public ObservableCollection<SFCTransitionViewModel> TransitionViewModels { get; } = new();
    public ObservableCollection<SFCBranchViewModel> BranchViewModels { get; } = new();
    public ObservableCollection<SFCConnectionViewModel> ConnectionViewModels { get; } = new();

    // 调试相关属性
    public SFCDebuggerService Debugger => _debugger;
    public bool IsDebugging => _debugger.IsDebugging;
    public bool IsPaused => _debugger.IsPaused;
    public SFCStepModel? CurrentDebugStep => _debugger.CurrentStep;

    // 命令
    public ICommand AddNodeCommand { get; private set; } = null!;
    public ICommand AddStepCommand { get; private set; } = null!;
    public ICommand AddTransitionCommand { get; private set; } = null!;
    public ICommand AddBranchCommand { get; private set; } = null!;

    public ICommand AddActionCommand { get; private set; } = null!;
    public ICommand DeleteElementCommand { get; private set; } = null!;
    public ICommand DeleteSelectedCommand { get; private set; } = null!;
    public ICommand ConnectElementsCommand { get; private set; } = null!;
    public ICommand ValidateSFCCommand { get; private set; } = null!;
    public ICommand GenerateCodeCommand { get; private set; } = null!;
    public ICommand SaveSFCCommand { get; private set; } = null!;
    public ICommand LoadSFCCommand { get; private set; } = null!;
    public ICommand NewSFCCommand { get; private set; } = null!;
    public ICommand ExportCodeCommand { get; private set; } = null!;
    public ICommand ClearSelectionCommand { get; private set; } = null!;

    // 西门子博图风格命令 - 插入元素
    public ICommand InsertStepCommand { get; private set; } = null!;
    public ICommand InsertTransitionCommand { get; private set; } = null!;
    public ICommand InsertBranchCommand { get; private set; } = null!;
    public ICommand InsertEndCommand { get; private set; } = null!;
    public ICommand InsertJumpCommand { get; private set; } = null!;
    public ICommand InsertSelectionBranchCommand { get; private set; } = null!;
    public ICommand InsertParallelBranchCommand { get; private set; } = null!;

    // 调试命令
    public ICommand StartDebuggingCommand { get; private set; } = null!;
    public ICommand StopDebuggingCommand { get; private set; } = null!;
    public ICommand PauseDebuggingCommand { get; private set; } = null!;
    public ICommand ResumeDebuggingCommand { get; private set; } = null!;
    public ICommand StepCommand { get; private set; } = null!;
    public ICommand RunCommand { get; private set; } = null!;
    public ICommand ResetCommand { get; private set; } = null!;
    public ICommand AddBreakpointCommand { get; private set; } = null!;
    public ICommand RemoveBreakpointCommand { get; private set; } = null!;
    public ICommand AddWatchVariableCommand { get; private set; } = null!;
    public ICommand CompileWithMatIECCommand { get; private set; } = null!;

    public IRelayCommand<SFCBranchModel> UpdateBranchCommand { get; private set; } = null!;
    public IRelayCommand<SFCBranchModel> ToggleConvergenceCommand { get; private set; } = null!;
    public IRelayCommand<object> InsertSiblingBranchCommand { get; }
    public IRelayCommand InsertSelectionBranchFromAnyPartCommand { get; }
    public IRelayCommand InsertParallelBranchFromAnyPartCommand { get; }

    public EnhancedSFCViewModel()
    {
        _validator = new SFCValidator();
        _codeGenerator = new SFCCodeGenerator();
        _debugger = new SFCDebuggerService();
        _compilerIntegration = new SFCCompilerIntegrationService();

        InitializeCommands();
        InitializeSampleData();
        InitializeDebugger();

        // 监听数据变化
        Steps.CollectionChanged += (s, e) => OnDataChanged();
        Transitions.CollectionChanged += (s, e) => OnDataChanged();
        Branches.CollectionChanged += (s, e) => OnDataChanged();
        Connections.CollectionChanged += (s, e) => OnDataChanged();
        Actions.CollectionChanged += (s, e) => OnDataChanged();

        UpdateBranchCommand = new RelayCommand<SFCBranchModel>(UpdateBranch);
        ToggleConvergenceCommand = new RelayCommand<SFCBranchModel>(ToggleConvergence);
        InsertSiblingBranchCommand = new RelayCommand<object>(InsertSiblingBranch);
        InsertSelectionBranchFromAnyPartCommand = new RelayCommand(InsertSelectionBranchFromAnyPart, CanInsertSelectionBranchFromAnyPart);
        InsertParallelBranchFromAnyPartCommand = new RelayCommand(InsertParallelBranchFromAnyPart, CanInsertParallelBranchFromAnyPart);
    }

    public SFCConnectionModel? AddConnection(string sourceId, string targetId, int sourceIndex, int targetIndex, LineStyle lineStyle = LineStyle.Solid)
    {
        System.Diagnostics.Debug.WriteLine($"[AddConnection] 开始执行: {sourceId} -> {targetId}");

        // Basic validation
        if (string.IsNullOrEmpty(sourceId) || string.IsNullOrEmpty(targetId))
        {
            System.Diagnostics.Debug.WriteLine($"[AddConnection] ❌ ID验证失败: sourceId='{sourceId}', targetId='{targetId}'");
            return null;
        }

        // More robust: find the source and target objects to determine their types
        object? sourceObject = Steps.FirstOrDefault(s => s.Id == sourceId) ??
                              (object?)Transitions.FirstOrDefault(t => t.Id == sourceId) ??
                              (object?)Branches.FirstOrDefault(b => b.Id == sourceId);

        object? targetObject = Steps.FirstOrDefault(s => s.Id == targetId) ??
                              (object?)Transitions.FirstOrDefault(t => t.Id == targetId) ??
                              (object?)Branches.FirstOrDefault(b => b.Id == targetId);

        System.Diagnostics.Debug.WriteLine($"[AddConnection] 对象查找结果: sourceObject={sourceObject?.GetType().Name ?? "null"}, targetObject={targetObject?.GetType().Name ?? "null"}");

        if (sourceObject == null || targetObject == null)
        {
            System.Diagnostics.Debug.WriteLine($"[AddConnection] ❌ 对象查找失败");
            return null;
        }


        // 计算连接路径点 - 使用精确的连接点位置
        // 优先使用ViewModel的位置，确保与UI显示一致
        var sourcePosition = GetElementPositionFromViewModel(sourceId) ?? GetElementPosition(sourceObject);
        var targetPosition = GetElementPositionFromViewModel(targetId) ?? GetElementPosition(targetObject);

        System.Diagnostics.Debug.WriteLine($"[AddConnection] 位置获取: 源ViewModel位置={GetElementPositionFromViewModel(sourceId)}, 源Model位置={GetElementPosition(sourceObject)}");
        System.Diagnostics.Debug.WriteLine($"[AddConnection] 位置获取: 目标ViewModel位置={GetElementPositionFromViewModel(targetId)}, 目标Model位置={GetElementPosition(targetObject)}");

        // 尝试获取精确的连接点位置（通过Canvas的FindConnectPointInElement）
        var sourcePoint = GetPreciseConnectPointPosition(sourceObject, sourcePosition, sourceIndex, true);
        var targetPoint = GetPreciseConnectPointPosition(targetObject, targetPosition, targetIndex, false);

        System.Diagnostics.Debug.WriteLine($"[AddConnection] 创建连接: {sourceId} -> {targetId}");
        System.Diagnostics.Debug.WriteLine($"[AddConnection] 源位置: {sourcePosition}, 目标位置: {targetPosition}");
        System.Diagnostics.Debug.WriteLine($"[AddConnection] 源对象类型: {sourceObject.GetType().Name}, 目标对象类型: {targetObject.GetType().Name}");
        System.Diagnostics.Debug.WriteLine($"[AddConnection] 源连接点: {sourcePoint}, 目标连接点: {targetPoint}");

        var connection = new SFCConnectionModel
        {
            Id = Guid.NewGuid().ToString(),
            SourceId = sourceId,
            TargetId = targetId,
            SourceType = GetElementType(sourceObject),
            TargetType = GetElementType(targetObject),
            SourceConnectPointIndex = sourceIndex,
            TargetConnectPointIndex = targetIndex,
            ConnectionType = SFCConnectionType.Normal, // Or determine based on logic
            LineStyle = lineStyle // 设置线条样式
        };

        // 设置路径点
        connection.PathPoints.Add(sourcePoint);
        connection.PathPoints.Add(targetPoint);

        System.Diagnostics.Debug.WriteLine($"[AddConnection] 连接创建完成，路径点数量: {connection.PathPoints.Count}");

        Connections.Add(connection);
        AddConnectionViewModel(connection);

        // 更新连接点状态
        UpdateConnectPointStates(sourceId, targetId, sourceIndex, targetIndex, connection.Id);

        // 强制更新UI中的连接线显示
        OnPropertyChanged(nameof(ConnectionViewModels));

        // 延迟更新连接线位置，确保UI元素已经渲染完成
        System.Windows.Application.Current.Dispatcher.BeginInvoke(new Action(() =>
        {
            // 触发连接线位置更新
            var connectionViewModel = ConnectionViewModels.FirstOrDefault(c => c.Id == connection.Id);
            if (connectionViewModel != null)
            {
                System.Diagnostics.Debug.WriteLine($"[AddConnection] 延迟更新连接线 {connection.Id} 的路径点");
                // 重新计算并设置路径点 - 使用ViewModel对象
                var sourceObj = StepViewModels.FirstOrDefault(s => s.Id == sourceId) ??
                               (object?)TransitionViewModels.FirstOrDefault(t => t.Id == sourceId) ??
                               (object?)BranchViewModels.FirstOrDefault(b => b.Id == sourceId);
                var targetObj = StepViewModels.FirstOrDefault(s => s.Id == targetId) ??
                               (object?)TransitionViewModels.FirstOrDefault(t => t.Id == targetId) ??
                               (object?)BranchViewModels.FirstOrDefault(b => b.Id == targetId);

                if (sourceObj != null && targetObj != null)
                {
                    var sourcePos = GetElementPosition(sourceObj);
                    var targetPos = GetElementPosition(targetObj);
                    var newSourcePoint = CalculateElementConnectPoint(sourceObj, sourcePos, true, sourceIndex);
                    var newTargetPoint = CalculateElementConnectPoint(targetObj, targetPos, false, targetIndex);

                    // 先保存新的路径点
                    var tempPoints = new List<Point> { newSourcePoint, newTargetPoint };

                    // 清空并重新添加路径点，这会触发ObservableCollection的CollectionChanged事件
                    connectionViewModel.PathPoints.Clear();
                    foreach (var point in tempPoints)
                    {
                        connectionViewModel.PathPoints.Add(point);
                    }

                    System.Diagnostics.Debug.WriteLine($"[AddConnection] 延迟更新 - 源对象类型: {sourceObj.GetType().Name}, 目标对象类型: {targetObj.GetType().Name}");
                    System.Diagnostics.Debug.WriteLine($"[AddConnection] 延迟更新 - 源位置: {sourcePos}, 目标位置: {targetPos}");
                    System.Diagnostics.Debug.WriteLine($"[AddConnection] 延迟更新后的路径点: {newSourcePoint} -> {newTargetPoint}");
                    System.Diagnostics.Debug.WriteLine($"[AddConnection] PathPoints集合已更新，应该触发重新评估");
                }
            }
        }), System.Windows.Threading.DispatcherPriority.Loaded);

        StatusMessage = $"✅ 创建连接: {sourceId} -> {targetId} (连接总数: {Connections.Count})";

        return connection;
    }

    /// <summary>
    /// 更新连接点状态
    /// </summary>
    private void UpdateConnectPointStates(string sourceId, string targetId, int sourceIndex, int targetIndex, string connectionId)
    {
        try
        {
            // 查找源元素和目标元素的ViewModel
            var sourceViewModel = FindElementViewModel(sourceId);
            var targetViewModel = FindElementViewModel(targetId);

            if (sourceViewModel != null)
            {
                // 更新源元素的输出连接点状态
                UpdateElementConnectPointState(sourceViewModel, sourceIndex, false, connectionId);
            }

            if (targetViewModel != null)
            {
                // 更新目标元素的输入连接点状态
                UpdateElementConnectPointState(targetViewModel, targetIndex, true, connectionId);
            }

            System.Diagnostics.Debug.WriteLine($"[UpdateConnectPointStates] 连接点状态更新完成: {sourceId}[{sourceIndex}] -> {targetId}[{targetIndex}]");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[UpdateConnectPointStates] 更新连接点状态失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 查找元素的ViewModel
    /// </summary>
    private object? FindElementViewModel(string elementId)
    {
        return StepViewModels.FirstOrDefault(s => s.Id == elementId) ??
               (object?)TransitionViewModels.FirstOrDefault(t => t.Id == elementId) ??
               (object?)BranchViewModels.FirstOrDefault(b => b.Id == elementId);
    }

    /// <summary>
    /// 更新元素连接点状态
    /// </summary>
    private void UpdateElementConnectPointState(object elementViewModel, int connectPointIndex, bool isInput, string connectionId)
    {
        try
        {
            // 根据ViewModel类型获取连接点适配器
            ObservableCollection<SFCConnectPointAdapter>? adapters = null;

            if (elementViewModel is SFCStepViewModel stepViewModel)
            {
                adapters = stepViewModel.ConnectPointAdapters;
            }
            else if (elementViewModel is SFCTransitionViewModel transitionViewModel)
            {
                adapters = transitionViewModel.ConnectPointAdapters;
            }
            else if (elementViewModel is SFCBranchViewModel branchViewModel)
            {
                adapters = branchViewModel.ConnectPointAdapters;
            }

            if (adapters != null && adapters.Count > connectPointIndex)
            {
                // 根据输入/输出类型选择正确的适配器
                SFCConnectPointAdapter? targetAdapter = null;

                if (isInput)
                {
                    // 查找输入连接点适配器（通常是第一个）
                    targetAdapter = adapters.FirstOrDefault(a => a.Direction == ConnectPointDirection.Input);
                }
                else
                {
                    // 查找输出连接点适配器（通常是第二个）
                    targetAdapter = adapters.FirstOrDefault(a => a.Direction == ConnectPointDirection.Output);
                }

                if (targetAdapter != null)
                {
                    // 添加连接到适配器
                    targetAdapter.AddConnection(connectionId);
                    System.Diagnostics.Debug.WriteLine($"[UpdateElementConnectPointState] ✅ 连接点状态更新成功: {elementViewModel.GetType().Name}, IsInput: {isInput}, ConnectionId: {connectionId}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[UpdateElementConnectPointState] ❌ 未找到匹配的连接点适配器: IsInput: {isInput}");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[UpdateElementConnectPointState] ❌ 连接点适配器集合为空或索引超出范围");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[UpdateElementConnectPointState] ❌ 更新连接点状态失败: {ex.Message}");
        }
    }

    private void InitializeCommands()
    {
        AddNodeCommand = new RelayCommand<NodeDefinition>(AddNode);
        AddStepCommand = new RelayCommand(() => AddStep(new Point(200, 200)));
        AddTransitionCommand = new RelayCommand(() => AddTransition(new Point(200, 300)));
        AddBranchCommand = new RelayCommand(() => AddBranch(new Point(200, 400)));

        AddActionCommand = new RelayCommand<SFCStepModel>(AddAction);
        DeleteElementCommand = new RelayCommand<object>(DeleteElement);
        DeleteSelectedCommand = new RelayCommand(DeleteSelectedElement, () => CanDeleteSelectedElement());
        ConnectElementsCommand = new RelayCommand<object>(ConnectElements);
        ValidateSFCCommand = new RelayCommand(ValidateSFC);
        GenerateCodeCommand = new RelayCommand(GenerateCode);
        SaveSFCCommand = new RelayCommand(SaveSFC);
        LoadSFCCommand = new RelayCommand(LoadSFC);
        NewSFCCommand = new RelayCommand(NewSFC);
        ExportCodeCommand = new RelayCommand(ExportCode);
        ClearSelectionCommand = new RelayCommand(ClearSelection);

        // 西门子博图风格命令 - 插入元素（需要选中元素才能执行）
        InsertStepCommand = new RelayCommand(InsertStepAfterSelected, () => CanInsertElement());
        InsertTransitionCommand = new RelayCommand(InsertTransitionAfterSelected, () => CanInsertElement());
        InsertBranchCommand = new RelayCommand(InsertBranchAfterSelected, () => CanInsertElement());
        InsertEndCommand = new RelayCommand(InsertEndAfterSelected, () => CanInsertElement());
        InsertJumpCommand = new RelayCommand<string>(InsertJumpAfterSelected, _ => CanInsertElement());
        InsertSelectionBranchCommand = new RelayCommand(InsertSelectionBranchAfterSelected, CanInsertSelectionBranch);
        InsertParallelBranchCommand = new RelayCommand(InsertParallelBranchAfterSelected, () => CanInsertElement());

        // 调试命令
        StartDebuggingCommand = new AsyncRelayCommand(StartDebugging);
        StopDebuggingCommand = new AsyncRelayCommand(StopDebugging);
        PauseDebuggingCommand = new RelayCommand(() => _debugger.PauseDebugging());
        ResumeDebuggingCommand = new RelayCommand(() => _debugger.ResumeDebugging());
        StepCommand = new RelayCommand(() => _debugger.StepAsync());
        RunCommand = new RelayCommand(() => _debugger.RunAsync());
        ResetCommand = new RelayCommand(() => _debugger.ResetAsync());
        AddBreakpointCommand = new RelayCommand<SFCStepModel>(AddBreakpoint);
        RemoveBreakpointCommand = new RelayCommand<SFCStepModel>(RemoveBreakpoint);
        AddWatchVariableCommand = new RelayCommand<string>(AddWatchVariable);
        CompileWithMatIECCommand = new AsyncRelayCommand(CompileWithMatIEC);

        UpdateBranchCommand = new RelayCommand<SFCBranchModel>(UpdateBranch);
        ToggleConvergenceCommand = new RelayCommand<SFCBranchModel>(ToggleConvergence);
    }

    /// <summary>
    /// 检查是否可以插入元素（西门子Graph风格：需要先选中元素）
    /// </summary>
    private bool CanInsertElement()
    {
        // 对于第一个步骤，允许在没有选中元素的情况下添加
        if (Steps.Count == 0)
        {
            System.Diagnostics.Debug.WriteLine("[CanInsertElement] 允许添加第一个步骤");
            return true;
        }

        // 其他情况下必须有选中元素
        var canInsert = SelectedElement != null;
        // 简化调试输出 - 只在需要时输出
        // System.Diagnostics.Debug.WriteLine($"[CanInsertElement] 选中元素: {SelectedElement?.GetType().Name ?? "null"}, 可以插入: {canInsert}");
        return canInsert;
    }

    /// <summary>
    /// 检查是否可以插入选择分支（支持在步骤或现有选择分支后插入）
    /// </summary>
    private bool CanInsertSelectionBranch()
    {
        // 对于第一个步骤，允许在没有选中元素的情况下添加
        if (Steps.Count == 0)
        {
            return true;
        }

        if (SelectedElement == null)
            return false;

        var elementType = GetElementType(SelectedElement);

        // 可以在步骤后插入选择分支
        if (elementType == SFCElementType.Step)
            return true;

        // 可以在现有选择分支后插入新的选择分支（多分支功能）
        if (elementType == SFCElementType.Branch && SelectedElement is SFCBranchViewModel branchViewModel)
        {
            return branchViewModel.BranchType == SFCBranchType.Selection;
        }

        return false;
    }

    /// <summary>
    /// 当选中元素改变时调用，更新命令状态
    /// </summary>
    private void OnSelectedElementChanged()
    {
        // 触发所有插入命令的CanExecuteChanged事件
        (InsertStepCommand as RelayCommand)?.NotifyCanExecuteChanged();
        (InsertTransitionCommand as RelayCommand)?.NotifyCanExecuteChanged();
        (InsertBranchCommand as RelayCommand)?.NotifyCanExecuteChanged();
        (InsertEndCommand as RelayCommand)?.NotifyCanExecuteChanged();
        (InsertJumpCommand as RelayCommand<string>)?.NotifyCanExecuteChanged();
        (InsertSelectionBranchCommand as RelayCommand)?.NotifyCanExecuteChanged();
        (InsertParallelBranchCommand as RelayCommand)?.NotifyCanExecuteChanged();
        (InsertSelectionBranchFromAnyPartCommand as RelayCommand)?.NotifyCanExecuteChanged();

        // 触发删除命令的CanExecuteChanged事件
        (DeleteSelectedCommand as RelayCommand)?.NotifyCanExecuteChanged();
    }

    private void AddNode(NodeDefinition? definition)
    {
        if (definition == null) return;

        // 根据具体的节点名称创建对应的SFC元素
        switch (definition.Name)
        {
            case "插入步":
            case "SFC步骤":
                AddStep(new Point(200, 200));
                break;
            case "转移条件":
            case "SFC转换":
                AddTransition(new Point(200, 300));
                break;
            case "并行分支":
                AddBranch(new Point(200, 400));
                break;
            case "选择分支":
                AddBranch(new Point(200, 400));
                break;
            case "等待条件":
            case "延时执行":
            case "流程结束":
            case "子流程调用":
                // 这些特殊步骤类型已被简化，统一创建普通步骤
                AddStep(new Point(200, 200));
                break;
            default:
                // 根据类别决定默认行为
                switch (definition.Category)
                {
                    case "流程控制":
                    case "SFC流程控制":
                    case "SFC基本元素":
                        if (definition.Name.Contains("步骤") || definition.Name.Contains("Step"))
                        {
                            AddStep(new Point(200, 200));
                        }
                        else if (definition.Name.Contains("转换") || definition.Name.Contains("Transition"))
                        {
                            AddTransition(new Point(200, 300));
                        }
                        else if (definition.Name.Contains("分支") || definition.Name.Contains("Branch"))
                        {
                            AddBranch(new Point(200, 400));
                        }
                        else
                        {
                            AddStep(new Point(200, 200));
                        }
                        break;
                    case "SFC分支结构":
                        AddBranch(new Point(200, 400));
                        break;
                    default:
                        AddStep(new Point(200, 200));
                        break;
                }
                break;
        }

        StatusMessage = $"已添加SFC元素: {definition.Name}";
    }









    /// <summary>
    /// 计算最佳创建位置（避免重叠）- 西门子Graph风格优化
    /// </summary>
    private Point CalculateOptimalPosition(Point preferredPosition)
    {
        const double minDistance = 0; // 最小间距（连接点完全重叠）
        const double gridSize = 20; // 网格大小
        const double verticalSpacing = 0; // 垂直间距（连接点完全重叠风格）

        // 获取所有现有元素的位置
        var existingPositions = GetAllElementPositions();

        // 检查首选位置是否可用
        if (IsPositionAvailable(preferredPosition, existingPositions, minDistance))
        {
            return SnapToGrid(preferredPosition, gridSize);
        }

        // 西门子Graph风格：优先在垂直方向搜索位置
        // 首先尝试在首选位置下方找位置
        for (int i = 1; i <= 10; i++)
        {
            var testPosition = new Point(preferredPosition.X, preferredPosition.Y + i * verticalSpacing);
            if (IsPositionInCanvas(testPosition) &&
                IsPositionAvailable(testPosition, existingPositions, minDistance))
            {
                return SnapToGrid(testPosition, gridSize);
            }
        }

        // 然后尝试在首选位置上方找位置
        for (int i = 1; i <= 5; i++)
        {
            var testPosition = new Point(preferredPosition.X, preferredPosition.Y - i * verticalSpacing);
            if (IsPositionInCanvas(testPosition) &&
                IsPositionAvailable(testPosition, existingPositions, minDistance))
            {
                return SnapToGrid(testPosition, gridSize);
            }
        }

        // 最后尝试水平偏移
        for (int i = 1; i <= 5; i++)
        {
            // 右侧
            var rightPosition = new Point(preferredPosition.X + i * 100, preferredPosition.Y);
            if (IsPositionInCanvas(rightPosition) &&
                IsPositionAvailable(rightPosition, existingPositions, minDistance))
            {
                return SnapToGrid(rightPosition, gridSize);
            }

            // 左侧
            var leftPosition = new Point(preferredPosition.X - i * 100, preferredPosition.Y);
            if (IsPositionInCanvas(leftPosition) &&
                IsPositionAvailable(leftPosition, existingPositions, minDistance))
            {
                return SnapToGrid(leftPosition, gridSize);
            }
        }

        // 如果找不到合适位置，返回首选位置（用户可以手动调整）
        return SnapToGrid(preferredPosition, gridSize);
    }

    /// <summary>
    /// 获取所有现有元素的位置
    /// </summary>
    private List<Point> GetAllElementPositions()
    {
        var positions = new List<Point>();

        // 添加步骤位置
        foreach (var step in Steps)
        {
            positions.Add(step.Position);
        }

        // 添加转换位置
        foreach (var transition in Transitions)
        {
            positions.Add(transition.Position);
        }

        // 添加分支位置
        foreach (var branch in Branches)
        {
            positions.Add(branch.Position);
        }

        return positions;
    }

    /// <summary>
    /// 检查位置是否可用（不与现有元素重叠）
    /// </summary>
    private bool IsPositionAvailable(Point position, List<Point> existingPositions, double minDistance)
    {
        foreach (var existingPos in existingPositions)
        {
            var distance = Math.Sqrt(Math.Pow(position.X - existingPos.X, 2) +
                                   Math.Pow(position.Y - existingPos.Y, 2));
            if (distance < minDistance)
            {
                return false;
            }
        }
        return true;
    }

    /// <summary>
    /// 检查位置是否在画布范围内
    /// </summary>
    private bool IsPositionInCanvas(Point position)
    {
        return position.X >= 0 && position.Y >= 0 &&
               position.X <= 3000 && position.Y <= 3000; // 使用固定画布大小
    }

    /// <summary>
    /// 对齐到网格
    /// </summary>
    private Point SnapToGrid(Point position, double gridSize)
    {
        return new Point(
            Math.Round(position.X / gridSize) * gridSize,
            Math.Round(position.Y / gridSize) * gridSize);
    }

    public void AddStep(Point position)
    {
        // 西门子Graph风格：必须先选中一个现有元素才能添加新步骤
        if (SelectedElement == null && Steps.Count > 0)
        {
            MessageBox.Show("请先选择一个现有元素，然后添加新步骤。\n新步骤将自动添加在选中元素下方并建立连接。",
                "西门子Graph风格提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        // 计算新步骤的位置
        Point optimalPosition;
        if (SelectedElement != null)
        {
            // 在选中元素下方添加，使用精确连接点对齐计算
            optimalPosition = CalculateConnectPointAlignedPosition(SelectedElement, STANDARD_STEP_SIZE);
        }
        else
        {
            // 第一个步骤，使用默认位置
            optimalPosition = CalculateOptimalPosition(position);
        }

        int stepNumber = GetNextStepNumber();
        var step = new SFCStepModel
        {
            Name = $"Step_{stepNumber}",
            Position = optimalPosition,
            StepNumber = stepNumber,
            IsInitialStep = Steps.Count == 0 // 第一个步骤设为初始步骤
        };

        Steps.Add(step);
        CurrentSFC.Steps.Add(step);

        // 创建对应的ViewModel用于UI显示
        var stepViewModel = new SFCStepViewModel
        {
            Id = step.Id,
            Name = step.Name,
            Position = step.Position,
            StepNumber = step.StepNumber,
            IsInitialStep = step.IsInitialStep,
            Description = step.Description
        };
        StepViewModels.Add(stepViewModel);

        // 西门子Graph风格：自动创建与选中元素的连接
        if (SelectedElement != null && Steps.Count > 1)
        {
            CreateAutoConnection(SelectedElement, step);
        }

        // 选中新创建的步骤
        SelectedElement = step;

        StatusMessage = $"添加了步骤: {step.Name}";
        IsModified = true;
    }

    public void AddTransition(Point position)
    {
        StatusMessage = "🔧 开始添加转换条件...";
        System.Diagnostics.Debug.WriteLine("[AddTransition] 开始执行");

        // 西门子Graph风格：必须先选中一个现有元素才能添加新转换
        if (SelectedElement == null)
        {
            StatusMessage = "❌ 请先选择一个现有元素";
            MessageBox.Show("请先选择一个现有元素，然后添加新转换。\n新转换将自动添加在选中元素下方并建立连接。",
                "西门子Graph风格提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        System.Diagnostics.Debug.WriteLine($"[AddTransition] 选中元素: {GetElementName(SelectedElement)} ({GetElementType(SelectedElement)})");

        // 验证选中元素是否可以连接到转换
        var selectedElementType = GetElementType(SelectedElement);
        if (selectedElementType != SFCElementType.Step && selectedElementType != SFCElementType.Branch)
        {
            MessageBox.Show("只有步骤或分支可以连接到转换。",
                "连接规则提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // 在选中元素下方添加转换，使用精确连接点对齐计算
        var optimalPosition = CalculateConnectPointAlignedPosition(SelectedElement, STANDARD_TRANSITION_SIZE);

        var transition = new SFCTransitionModel
        {
            Name = $"Transition_{Transitions.Count + 1}",
            Position = optimalPosition,
            TransitionNumber = Transitions.Count + 1,
            ConditionExpression = "TRUE"
        };

        Transitions.Add(transition);
        CurrentSFC.Transitions.Add(transition);

        // 创建对应的ViewModel
        var transitionViewModel = new SFCTransitionViewModel
        {
            Id = transition.Id,
            Name = transition.Name,
            Position = transition.Position,
            TransitionNumber = transition.TransitionNumber,
            ConditionExpression = transition.ConditionExpression ?? "TRUE"
        };
        TransitionViewModels.Add(transitionViewModel);

        // 西门子Graph风格：自动创建与选中元素的连接
        var selectedElementBeforeConnection = SelectedElement;
        CreateAutoConnection(selectedElementBeforeConnection, transition);

        // 选中新创建的转换
        SelectedElement = transition;

        StatusMessage = $"添加了转换: {transition.Name}";
        IsModified = true;
    }

    public void AddBranch(Point position)
    {
        // 西门子Graph风格：必须先选中一个现有元素才能添加新分支
        if (SelectedElement == null)
        {
            MessageBox.Show("请先选择一个现有元素，然后添加新分支。\n新分支将自动添加在选中元素下方并建立连接。",
                "西门子Graph风格提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        // 验证选中元素是否可以连接到分支
        var selectedElementType = GetElementType(SelectedElement);
        if (selectedElementType != SFCElementType.Step && selectedElementType != SFCElementType.Transition)
        {
            MessageBox.Show("只有步骤或转换可以连接到分支。",
                "连接规则提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // 在选中元素下方添加分支，使用精确连接点对齐计算
        var branchSize = new Size(120, 12); // 标准分支尺寸
        var optimalPosition = CalculateConnectPointAlignedPosition(SelectedElement, branchSize);

        var branch = new SFCBranchModel
        {
            Name = $"Branch_{Branches.Count + 1}",
            Position = optimalPosition,
            BranchType = SFCBranchType.Selection,
            IsConvergence = false,
            Size = new Size(120, 12)
        };

        Branches.Add(branch);
        CurrentSFC.Branches.Add(branch);

        // 创建对应的ViewModel
        var branchViewModel = new SFCBranchViewModel
        {
            Id = branch.Id,
            Name = branch.Name,
            BranchType = branch.BranchType,
            Position = branch.Position,
            Size = branch.Size,
            IsConvergence = branch.IsConvergence,
            InputStepIds = branch.InputStepIds,
            OutputStepIds = branch.OutputStepIds,
            InternalTransitionNumber = branch.InternalTransition?.TransitionNumber ?? 0,
            InternalTransitionCondition = branch.InternalTransition?.ConditionExpression ?? "TRUE"
        };

        // 设置命令
        branchViewModel.SelectCommand = new RelayCommand(() => SelectBranch(branchViewModel));
        branchViewModel.DeleteCommand = new RelayCommand(() => DeleteBranch(branchViewModel));
        branchViewModel.EditPropertiesCommand = new RelayCommand(() => EditBranch(branchViewModel));
        branchViewModel.StartConnectionCommand = new RelayCommand(() => StartConnection(branchViewModel));

        BranchViewModels.Add(branchViewModel);

        // 西门子Graph风格：自动创建与选中元素的连接
        CreateAutoConnection(SelectedElement, branch);

        // 选中新创建的分支
        SelectedElement = branch;

        StatusMessage = $"添加了分支: {branch.Name}";
        IsModified = true;
    }

    // 添加分支相关方法
    private void SelectBranch(SFCBranchViewModel branch)
    {
        // 清除其他选择
        foreach (var step in StepViewModels)
            step.IsSelected = false;
        foreach (var transition in TransitionViewModels)
            transition.IsSelected = false;
        foreach (var b in BranchViewModels)
            b.IsSelected = b == branch;

        SelectedElement = Branches.FirstOrDefault(b => b.Id == branch.Id);

        // 根据选中部分显示不同的状态消息
        string statusMessage = branch.BranchType == SFCBranchType.Selection && !string.IsNullOrEmpty(branch.SelectedPart) && branch.SelectedPart != "None"
            ? branch.SelectedPart switch
            {
                "Left" => $"选中分支: {branch.Name}左侧",
                "Right" => $"选中分支: {branch.Name}右侧转换条件",
                _ => $"选中分支: {branch.Name}"
            }
            : $"选中分支: {branch.Name}";

        StatusMessage = statusMessage;
    }

    // 添加StartConnection方法
    public void StartConnection(object element)
    {
        IsConnecting = true;
        ConnectionSource = element;
        StatusMessage = "选择连接目标...";
    }

    private void DeleteBranch(SFCBranchViewModel branchViewModel)
    {
        var branch = Branches.FirstOrDefault(b => b.Id == branchViewModel.Id);
        if (branch != null)
        {
            DeleteElement(branch);
            BranchViewModels.Remove(branchViewModel);
        }
    }

    public void UpdateBranch(SFCBranchModel branch)
    {
        // 查找现有的分支
        var existingBranch = Branches.FirstOrDefault(b => b.Id == branch.Id);
        if (existingBranch == null) return;

        // 更新分支属性
        existingBranch.Name = branch.Name;
        existingBranch.BranchType = branch.BranchType;
        existingBranch.IsConvergence = branch.IsConvergence;
        existingBranch.InputStepIds = branch.InputStepIds;
        existingBranch.OutputStepIds = branch.OutputStepIds;

        // 更新对应的ViewModel
        var branchViewModel = BranchViewModels.FirstOrDefault(bvm => bvm.Id == branch.Id);
        if (branchViewModel != null)
        {
            branchViewModel.Name = branch.Name;
            branchViewModel.BranchType = branch.BranchType;
            branchViewModel.IsConvergence = branch.IsConvergence;
            branchViewModel.InputStepIds = branch.InputStepIds;
            branchViewModel.OutputStepIds = branch.OutputStepIds;
        }

        StatusMessage = $"更新了分支: {branch.Name}";
        IsModified = true;
    }

    public void EditBranch(SFCBranchViewModel branchViewModel)
    {
        var branch = Branches.FirstOrDefault(b => b.Id == branchViewModel.Id);
        if (branch == null) return;

        // 创建分支编辑器视图模型
        var editViewModel = new SFCBranchEditorViewModel(branch)
        {
            SaveCommand = new RelayCommand<SFCBranchModel>(UpdateBranch),
            CancelCommand = new RelayCommand(() => { /* 关闭对话框 */ })
        };

        // 显示分支编辑对话框
        var dialog = new Views.SFCBranchEditorView
        {
            DataContext = editViewModel
        };

        dialog.ShowDialog();
    }

    // 添加分支到集合的辅助方法
    private void AddBranchToCollections(SFCBranchModel branch)
    {
        System.Diagnostics.Debug.WriteLine($"[分支集合] 添加分支，位置: {branch.Position}");

        // 添加到模型集合
        Branches.Add(branch);

        // 创建ViewModel
        var branchViewModel = new SFCBranchViewModel
        {
            Id = branch.Id,
            Name = branch.Name,
            BranchType = branch.BranchType,
            Position = branch.Position,
            Size = branch.Size,
            IsConvergence = branch.IsConvergence,
            ViewType = branch.ViewType, // 同步ViewType属性
            InputStepIds = branch.InputStepIds,
            OutputStepIds = branch.OutputStepIds,
            InternalTransitionNumber = branch.InternalTransition?.TransitionNumber ?? 0,
            InternalTransitionCondition = branch.InternalTransition?.ConditionExpression ?? "TRUE"
        };

        System.Diagnostics.Debug.WriteLine($"[分支集合] ViewModel位置: {branchViewModel.Position}");
        
        // 设置命令
        branchViewModel.SelectCommand = new RelayCommand(() => SelectBranch(branchViewModel));
        branchViewModel.DeleteCommand = new RelayCommand(() => DeleteBranch(branchViewModel));
        branchViewModel.EditPropertiesCommand = new RelayCommand(() => EditBranch(branchViewModel));
        branchViewModel.StartConnectionCommand = new RelayCommand(() => StartConnection(branchViewModel));
        
        // 添加到ViewModel集合
        BranchViewModels.Add(branchViewModel);
        
        // 添加到当前SFC模型
        CurrentSFC.Branches.Add(branch);
        
        StatusMessage = $"已添加分支: {branch.Name}";
        IsModified = true;
    }

    private void AddAction(SFCStepModel? step)
    {
        if (step == null) return;
        
        var action = new SFCActionModel
        {
            Name = $"Action_{step.Actions.Count + 1}",
            ActionType = SFCActionType.Normal,
            Qualifier = SFCActionQualifier.N,
            ActionExpression = "// 在此添加动作代码"
        };
        
        step.Actions.Add(action);
        Actions.Add(action);
        
        StatusMessage = $"为步骤 {step.Name} 添加了动作: {action.Name}";
        IsModified = true;
    }

    private void DeleteElement(object? element)
    {
        if (element == null) return;

        switch (element)
        {
            case SFCStepModel step:
                // 检查是否可以删除此步骤
                if (!CanDeleteStep(step))
                {
                    return; // 不允许删除，直接返回
                }

                // 删除相关连接
                var stepConnections = Connections.Where(c => c.SourceId == step.Id || c.TargetId == step.Id).ToList();
                foreach (var conn in stepConnections)
                {
                    Connections.Remove(conn);
                    CurrentSFC.Connections.Remove(conn);

                    // 同时删除对应的视图模型连接
                    var connViewModel = ConnectionViewModels.FirstOrDefault(cvm => cvm.Id == conn.Id);
                    if (connViewModel != null)
                    {
                        ConnectionViewModels.Remove(connViewModel);
                    }
                }

                // 删除数据模型
                Steps.Remove(step);
                CurrentSFC.Steps.Remove(step);

                // 删除对应的视图模型
                var stepViewModel = StepViewModels.FirstOrDefault(s => s.Id == step.Id);
                if (stepViewModel != null)
                {
                    StepViewModels.Remove(stepViewModel);
                }

                StatusMessage = $"删除了步骤: {step.Name}";
                break;

            case SFCTransitionModel transition:
                // 删除相关连接
                var transitionConnections = Connections.Where(c => c.SourceId == transition.Id || c.TargetId == transition.Id).ToList();
                foreach (var conn in transitionConnections)
                {
                    Connections.Remove(conn);
                    CurrentSFC.Connections.Remove(conn);
                    
                    // 同时删除对应的视图模型连接
                    var connViewModel = ConnectionViewModels.FirstOrDefault(cvm => cvm.Id == conn.Id);
                    if (connViewModel != null)
                    {
                        ConnectionViewModels.Remove(connViewModel);
                    }
                }
                
                // 删除数据模型
                Transitions.Remove(transition);
                CurrentSFC.Transitions.Remove(transition);
                
                // 删除对应的视图模型
                var transitionViewModel = TransitionViewModels.FirstOrDefault(t => t.Id == transition.Id);
                if (transitionViewModel != null)
                {
                    TransitionViewModels.Remove(transitionViewModel);
                }
                
                StatusMessage = $"删除了转换: {transition.Name}";
                break;
                
            case SFCBranchModel branch:
                // 删除相关连接
                var branchConnections = Connections.Where(c => c.SourceId == branch.Id || c.TargetId == branch.Id).ToList();
                foreach (var conn in branchConnections)
                {
                    Connections.Remove(conn);
                    CurrentSFC.Connections.Remove(conn);
                    
                    // 同时删除对应的视图模型连接
                    var connViewModel = ConnectionViewModels.FirstOrDefault(cvm => cvm.Id == conn.Id);
                    if (connViewModel != null)
                    {
                        ConnectionViewModels.Remove(connViewModel);
                    }
                }
                
                // 删除数据模型
                Branches.Remove(branch);
                CurrentSFC.Branches.Remove(branch);
                
                // 删除对应的视图模型
                var branchViewModel = BranchViewModels.FirstOrDefault(b => b.Id == branch.Id);
                if (branchViewModel != null)
                {
                    BranchViewModels.Remove(branchViewModel);
                }
                
                StatusMessage = $"删除了分支: {branch.Name}";
                break;
                
            case SFCConnectionModel connection:
                // 删除数据模型
                Connections.Remove(connection);
                CurrentSFC.Connections.Remove(connection);
                
                // 删除对应的视图模型
                var connectionViewModel = ConnectionViewModels.FirstOrDefault(c => c.Id == connection.Id);
                if (connectionViewModel != null)
                {
                    ConnectionViewModels.Remove(connectionViewModel);
                }
                
                StatusMessage = "删除了连接";
                break;
                
            case SFCStepViewModel viewModelStep:
                // 查找对应的数据模型
                var stepModelToDelete = Steps.FirstOrDefault(s => s.Id == viewModelStep.Id);
                if (stepModelToDelete != null)
                {
                    // 递归调用以删除模型和相关连接
                    DeleteElement(stepModelToDelete);
                }
                break;
                
            case SFCTransitionViewModel viewModelTrans:
                // 查找对应的数据模型
                var transModelToDelete = Transitions.FirstOrDefault(t => t.Id == viewModelTrans.Id);
                if (transModelToDelete != null)
                {
                    // 递归调用以删除模型和相关连接
                    DeleteElement(transModelToDelete);
                }
                break;
                
            case SFCBranchViewModel viewModelBranch:
                // 查找对应的数据模型
                var branchModelToDelete = Branches.FirstOrDefault(b => b.Id == viewModelBranch.Id);
                if (branchModelToDelete != null)
                {
                    // 递归调用以删除模型和相关连接
                    DeleteElement(branchModelToDelete);
                }
                break;
                
            case SFCConnectionViewModel connViewModel:
                // 查找对应的数据模型
                var connModel = Connections.FirstOrDefault(c => c.Id == connViewModel.Id);
                if (connModel != null)
                {
                    // 递归调用以删除模型
                    DeleteElement(connModel);
                }
                break;
        }
        
        IsModified = true;
        
        // 通知UI更新
        OnPropertyChanged(nameof(Steps));
        OnPropertyChanged(nameof(Transitions));
        OnPropertyChanged(nameof(Branches));
        OnPropertyChanged(nameof(Connections));
        OnPropertyChanged(nameof(StepViewModels));
        OnPropertyChanged(nameof(TransitionViewModels));
        OnPropertyChanged(nameof(BranchViewModels));
        OnPropertyChanged(nameof(ConnectionViewModels));
    }

    /// <summary>
    /// 检查是否可以删除指定的步骤
    /// </summary>
    /// <param name="step">要检查的步骤</param>
    /// <returns>如果可以删除返回true，否则返回false</returns>
    private bool CanDeleteStep(SFCStepModel step)
    {
        // 检查是否是初始步骤
        if (step.IsInitialStep)
        {
            StatusMessage = "❌ 不能删除初始步骤！初始步骤是SFC流程的起点，必须保留。";
            return false;
        }

        // 检查是否是最后一个步骤
        if (Steps.Count <= 1)
        {
            StatusMessage = "❌ 不能删除最后一个步骤！SFC流程必须至少包含一个步骤。";
            return false;
        }

        // 如果删除后没有初始步骤，也不允许删除
        var otherSteps = Steps.Where(s => s.Id != step.Id).ToList();
        if (!otherSteps.Any(s => s.IsInitialStep))
        {
            StatusMessage = "❌ 删除此步骤后将没有初始步骤！请先设置其他步骤为初始步骤。";
            return false;
        }

        return true;
    }

    private void ConnectElements(object? targetElement)
    {
        if (IsConnecting && ConnectionSource != null && targetElement != null && ConnectionSource != targetElement)
        {
            // 创建连接
            var connection = CreateConnection(ConnectionSource, targetElement);
            if (connection != null)
            {
                Connections.Add(connection);
                CurrentSFC.Connections.Add(connection);
                StatusMessage = "创建了新连接";
                IsModified = true;
            }
            
            IsConnecting = false;
            ConnectionSource = null;
        }
        else if (!IsConnecting && targetElement != null)
        {
            IsConnecting = true;
            ConnectionSource = targetElement;
            StatusMessage = "选择连接目标...";
        }
    }

    private SFCConnectionModel? CreateConnection(object source, object target)
    {
        string sourceId = GetElementId(source);
        string targetId = GetElementId(target);
        
        if (string.IsNullOrEmpty(sourceId) || string.IsNullOrEmpty(targetId))
            return null;
        
        // 获取元素类型
        var sourceType = GetElementType(source);
        var targetType = GetElementType(target);
        
        // 检查连接是否符合西门子Graph标准规则
        if (!IsValidConnectionByGraphStyle(sourceType, targetType))
        {
            // 错误消息已在验证方法中设置到StatusMessage
            MessageBox.Show(StatusMessage, "连接规则错误", MessageBoxButton.OK, MessageBoxImage.Warning);
            return null;
        }
        
        // 保留原有IEC标准验证（双重保险）
        if (!IsValidConnectionByIEC61131(sourceType, targetType))
        {
            // 给出友好提示
            switch (sourceType)
            {
                case SFCElementType.Step:
                    StatusMessage = "步骤只能连接到转换或分支";
                    break;
                case SFCElementType.Transition:
                    StatusMessage = "转换只能连接到步骤或分支";
                    break;
                case SFCElementType.Branch:
                    // 分支连接规则由分支类型决定，在IsValidBranchConnection中处理
                    break;
                default:
                    StatusMessage = "无效的连接";
                    break;
            }
            return null;
        }
        
        // 检查分支连接的特殊规则
        if ((sourceType == SFCElementType.Branch || targetType == SFCElementType.Branch) && 
            !IsValidBranchConnection(source, target))
        {
            return null;
        }
        
        // 检查是否已存在相同连接
        if (Connections.Any(c => c.SourceId == sourceId && c.TargetId == targetId))
        {
            StatusMessage = "连接已存在";
            return null;
        }
        
        // 确定连接类型
        var connectionType = DetermineConnectionType(source, target);
            
        // 计算连接路径点 - 使用精确的连接点位置
        var sourcePosition = GetElementPosition(source);
        var targetPosition = GetElementPosition(target);

        // 调试信息
        System.Diagnostics.Debug.WriteLine($"[连接调试] 源位置: {sourcePosition}, 目标位置: {targetPosition}");

        // 使用精确的连接点计算方法
        var sourcePoint = CalculateElementConnectPoint(source, sourcePosition, true);  // 输出连接点
        var targetPoint = CalculateElementConnectPoint(target, targetPosition, false); // 输入连接点

        System.Diagnostics.Debug.WriteLine($"[连接调试] 精确源连接点: {sourcePoint}, 精确目标连接点: {targetPoint}");

        // 创建新的连接
        var connection = new SFCConnectionModel
        {
            SourceId = sourceId,
            TargetId = targetId,
            SourceType = sourceType,
            TargetType = targetType,
            ConnectionType = connectionType
        };

        // 设置路径点
        connection.PathPoints.Add(sourcePoint);
        connection.PathPoints.Add(targetPoint);

        System.Diagnostics.Debug.WriteLine($"[连接调试] 连接创建完成，路径点数量: {connection.PathPoints.Count}");
        
        // 处理分支相关的连接
        if (source is SFCBranchViewModel sourceBranchVM)
        {
            // 更新分支的输出连接
            var sourceBranch = Branches.FirstOrDefault(b => b.Id == sourceBranchVM.Id);
            if (sourceBranch != null && !sourceBranch.OutputStepIds.Contains(targetId))
            {
                sourceBranch.OutputStepIds.Add(targetId);
            }
        }
        else if (source is SFCBranchModel sourceBranch)
        {
            // 如果源是分支，更新分支的输出连接
            if (!sourceBranch.OutputStepIds.Contains(targetId))
            {
                sourceBranch.OutputStepIds.Add(targetId);
            }
        }
        
        if (target is SFCBranchViewModel targetBranchVM)
        {
            // 更新分支的输入连接
            var targetBranch = Branches.FirstOrDefault(b => b.Id == targetBranchVM.Id);
            if (targetBranch != null && !targetBranch.InputStepIds.Contains(sourceId))
            {
                targetBranch.InputStepIds.Add(sourceId);
            }
        }
        else if (target is SFCBranchModel targetBranch)
        {
            // 如果目标是分支，更新分支的输入连接
            if (!targetBranch.InputStepIds.Contains(sourceId))
            {
                targetBranch.InputStepIds.Add(sourceId);
            }
        }
        
        // 添加对应的ConnectionViewModel
        AddConnectionViewModel(connection);
        
        StatusMessage = $"创建了新的{(connectionType == SFCConnectionType.Normal ? "连接" : 
                                      connectionType == SFCConnectionType.Jump ? "跳转连接" : "返回连接")}";
        
        return connection;
    }

    /// <summary>
    /// 西门子Graph风格：自动创建连接
    /// </summary>
    private void CreateAutoConnection(object sourceElement, object targetElement)
    {
        try
        {
            if (sourceElement == null || targetElement == null)
            {
                StatusMessage = "自动连接失败: 源元素或目标元素为空";
                return;
            }

            var sourceType = GetElementType(sourceElement);
            var targetType = GetElementType(targetElement);
            var sourceName = GetElementName(sourceElement);
            var targetName = GetElementName(targetElement);

            StatusMessage = $"尝试创建连接: {sourceName}({sourceType}) -> {targetName}({targetType})";

            // 验证连接是否符合SFC规则
            if (!IsValidConnectionByIEC61131(sourceType, targetType))
            {
                StatusMessage = $"连接不符合IEC61131规则，尝试插入中间元素: {sourceType} -> {targetType}";
                // 如果不能直接连接，根据规则插入中间元素
                CreateAutoConnectionWithIntermediateElement(sourceElement, targetElement);
                return;
            }

            // 直接创建连接
            var connection = CreateConnection(sourceElement, targetElement);
            if (connection != null)
            {
                Connections.Add(connection);
                CurrentSFC.Connections.Add(connection);

                // 创建ConnectionViewModel用于UI显示
                var connectionViewModel = new SFCConnectionViewModel
                {
                    Id = connection.Id,
                    SourceId = connection.SourceId,
                    TargetId = connection.TargetId,
                    SourceType = connection.SourceType,
                    TargetType = connection.TargetType,
                    ConnectionType = connection.ConnectionType
                };

                // 复制路径点到ViewModel
                foreach (var point in connection.PathPoints)
                {
                    connectionViewModel.PathPoints.Add(point);
                }

                ConnectionViewModels.Add(connectionViewModel);

                StatusMessage = $"✅ 自动创建连接成功: {sourceName} -> {targetName} (连接总数: {Connections.Count}, UI连接数: {ConnectionViewModels.Count})";

                System.Diagnostics.Debug.WriteLine($"[连接调试] 连接ViewModel已创建，路径点: {string.Join(", ", connectionViewModel.PathPoints)}");

                // 强制UI更新
                OnPropertyChanged(nameof(Connections));
                OnPropertyChanged(nameof(ConnectionViewModels));
            }
            else
            {
                StatusMessage = $"❌ 创建连接失败: CreateConnection返回null";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 自动连接异常: {ex.Message}";
        }
    }

    /// <summary>
    /// 创建带中间元素的自动连接
    /// </summary>
    private void CreateAutoConnectionWithIntermediateElement(object sourceElement, object targetElement)
    {
        var sourceType = GetElementType(sourceElement);
        var targetType = GetElementType(targetElement);
        var sourcePosition = GetElementPosition(sourceElement);
        var targetPosition = GetElementPosition(targetElement);

        // 计算中间元素位置
        var intermediatePosition = new Point(
            sourcePosition.X,
            sourcePosition.Y + (targetPosition.Y - sourcePosition.Y) / 2
        );

        // 根据源和目标类型决定需要插入的中间元素
        if (sourceType == SFCElementType.Step && targetType == SFCElementType.Step)
        {
            // 步骤到步骤：插入转换
            var transition = new SFCTransitionModel
            {
                Name = $"Auto_Transition_{Transitions.Count + 1}",
                Position = intermediatePosition,
                TransitionNumber = Transitions.Count + 1,
                ConditionExpression = "TRUE"
            };

            Transitions.Add(transition);
            CurrentSFC.Transitions.Add(transition);

            var transitionViewModel = new SFCTransitionViewModel
            {
                Id = transition.Id,
                Name = transition.Name,
                Position = transition.Position,
                TransitionNumber = transition.TransitionNumber,
                ConditionExpression = transition.ConditionExpression ?? "TRUE"
            };
            TransitionViewModels.Add(transitionViewModel);

            // 创建连接：源步骤 -> 转换 -> 目标步骤
            CreateConnection(sourceElement, transition);
            CreateConnection(transition, targetElement);

            StatusMessage = $"自动插入转换并创建连接";
        }
        else if (sourceType == SFCElementType.Transition && targetType == SFCElementType.Transition)
        {
            // 转换到转换：插入步骤
            int stepNumber = GetNextStepNumber();
            var step = new SFCStepModel
            {
                Name = $"Auto_Step_{stepNumber}",
                Position = intermediatePosition,
                StepNumber = stepNumber,
                IsInitialStep = false
            };

            Steps.Add(step);
            CurrentSFC.Steps.Add(step);

            var stepViewModel = new SFCStepViewModel
            {
                Id = step.Id,
                Name = step.Name,
                Position = step.Position,
                StepNumber = step.StepNumber,
                IsInitialStep = step.IsInitialStep,
                Description = step.Description
            };
            StepViewModels.Add(stepViewModel);

            // 创建连接：源转换 -> 步骤 -> 目标转换
            CreateConnection(sourceElement, step);
            CreateConnection(step, targetElement);

            StatusMessage = $"自动插入步骤并创建连接";
        }
    }

    /// <summary>
    /// 检查连接是否符合IEC 61131-3标准规则
    /// </summary>
    private bool IsValidConnectionByIEC61131(SFCElementType sourceType, SFCElementType targetType)
    {
        // 基本SFC连接规则：
        // 1. 步骤只能连接到转换或分支
        // 2. 转换只能连接到步骤或分支
        
        if (sourceType == SFCElementType.Step)
        {
            return targetType == SFCElementType.Transition || targetType == SFCElementType.Branch;
        }
        else if (sourceType == SFCElementType.Transition)
        {
            return targetType == SFCElementType.Step || targetType == SFCElementType.Branch;
        }
        else if (sourceType == SFCElementType.Branch)
        {
            // 分支连接规则由分支类型决定，在IsValidBranchConnection中处理
            return true;
        }
        
        return false;
    }
    
    /// <summary>
    /// 检查分支连接的特殊规则
    /// </summary>
    private bool IsValidBranchConnection(object source, object target)
    {
        // 获取分支类型和汇聚状态
        SFCBranchType branchType = SFCBranchType.Selection;
        bool isConvergence = false;
        
        if (source is SFCBranchViewModel sourceBranchVM)
        {
            branchType = sourceBranchVM.BranchType;
            isConvergence = sourceBranchVM.IsConvergence;
            
            // 分支作为源的规则
            if (isConvergence)
            {
                // 汇聚分支的输出只能是转换
                if (GetElementType(target) != SFCElementType.Transition)
                {
                    StatusMessage = "汇聚分支的输出只能是转换";
                    return false;
                }
            }
            else
            {
                // 非汇聚分支的规则
                if (branchType == SFCBranchType.Parallel)
                {
                    // 并行分支的输出只能是步骤
                    if (GetElementType(target) != SFCElementType.Step)
                    {
                        StatusMessage = "并行分支的输出只能是步骤";
                        return false;
                    }
                }
                else if (branchType == SFCBranchType.Selection)
                {
                    // 选择分支的输出只能是转换
                    if (GetElementType(target) != SFCElementType.Transition)
                    {
                        StatusMessage = "选择分支的输出只能是转换";
                        return false;
                    }
                }
            }
        }
        else if (target is SFCBranchViewModel targetBranchVM)
        {
            branchType = targetBranchVM.BranchType;
            isConvergence = targetBranchVM.IsConvergence;
            
            // 分支作为目标的规则
            if (!isConvergence)
            {
                // 非汇聚分支的输入只能是步骤
                if (GetElementType(source) != SFCElementType.Step)
                {
                    StatusMessage = "分支的输入只能是步骤";
                    return false;
                }
            }
            else
            {
                // 汇聚分支的输入只能是步骤
                if (GetElementType(source) != SFCElementType.Step)
                {
                    StatusMessage = "汇聚分支的输入只能是步骤";
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /// <summary>
    /// 确定连接类型（普通、跳转或返回）
    /// </summary>
    private SFCConnectionType DetermineConnectionType(object source, object target)
    {
        // 默认为普通连接
        var connectionType = SFCConnectionType.Normal;
        
        if (source is SFCStepViewModel || source is SFCStepModel)
        {
            if (target is SFCStepViewModel || target is SFCStepModel)
            {
                // 从步骤到步骤的连接被视为跳转连接
                connectionType = SFCConnectionType.Jump;
            }
        }
        else if (source is SFCTransitionViewModel || source is SFCTransitionModel)
        {
            // 判断是否为返回连接（通常是循环结构的一部分）
            var sourceId = GetElementId(source);
            var targetId = GetElementId(target);
            
            // 检查目标步骤是否已经存在到源转换的路径
            bool hasPathToSource = CheckIfPathExists(targetId, sourceId);
            
            if (hasPathToSource)
            {
                connectionType = SFCConnectionType.Return;
            }
        }
        
        return connectionType;
    }
    
    /// <summary>
    /// 检查两个元素之间是否已存在路径
    /// </summary>
    private bool CheckIfPathExists(string startId, string endId)
    {
        // 使用BFS算法检查从startId到endId是否存在路径
        var visited = new HashSet<string>();
        var queue = new Queue<string>();
        
        queue.Enqueue(startId);
        visited.Add(startId);
        
        while (queue.Count > 0)
        {
            var currentId = queue.Dequeue();
            
            // 找到所有从当前元素出发的连接
            var outgoingConnections = Connections.Where(c => c.SourceId == currentId).ToList();
            
            foreach (var conn in outgoingConnections)
            {
                if (conn.TargetId == endId)
                {
                    return true; // 找到路径
                }
                
                if (!visited.Contains(conn.TargetId))
                {
                    visited.Add(conn.TargetId);
                    queue.Enqueue(conn.TargetId);
                }
            }
        }
        
        return false; // 未找到路径
    }
    
    /// <summary>
    /// 添加连接视图模型
    /// </summary>
    private void AddConnectionViewModel(SFCConnectionModel connection)
    {
        var connectionViewModel = new SFCConnectionViewModel
        {
            Id = connection.Id,
            SourceId = connection.SourceId,
            TargetId = connection.TargetId,
            SourceType = connection.SourceType,
            TargetType = connection.TargetType,
            ConnectionType = connection.ConnectionType,
            SourceConnectPointIndex = connection.SourceConnectPointIndex,
            TargetConnectPointIndex = connection.TargetConnectPointIndex,
            LineStyle = connection.LineStyle,
            LineColor = connection.LineColor,
            LineThickness = connection.LineThickness
        };
        
        // 为连接设置命令
        connectionViewModel.SelectCommand = new RelayCommand(() => SelectConnection(connectionViewModel));
        connectionViewModel.DeleteCommand = new RelayCommand(() => DeleteConnection(connectionViewModel));
        
        // 添加路径点
        foreach (var point in connection.PathPoints)
        {
            connectionViewModel.PathPoints.Add(point);
        }

        System.Diagnostics.Debug.WriteLine($"[AddConnectionViewModel] 连接 {connection.Id} 路径点数量: {connection.PathPoints.Count}");
        System.Diagnostics.Debug.WriteLine($"[AddConnectionViewModel] ViewModel路径点数量: {connectionViewModel.PathPoints.Count}");
        if (connectionViewModel.PathPoints.Count >= 2)
        {
            System.Diagnostics.Debug.WriteLine($"[AddConnectionViewModel] 起点: {connectionViewModel.PathPoints[0]}, 终点: {connectionViewModel.PathPoints[connectionViewModel.PathPoints.Count - 1]}");
        }

        ConnectionViewModels.Add(connectionViewModel);
    }
    
    /// <summary>
    /// 选择连接
    /// </summary>
    private void SelectConnection(SFCConnectionViewModel connection)
    {
        // 清除其他元素的选择状态
        foreach (var step in StepViewModels)
            step.IsSelected = false;
        foreach (var transition in TransitionViewModels)
            transition.IsSelected = false;
        foreach (var branch in BranchViewModels)
            branch.IsSelected = false;
        foreach (var conn in ConnectionViewModels)
            conn.IsSelected = conn == connection;
        
        // 设置当前选中元素
        SelectedElement = Connections.FirstOrDefault(c => c.Id == connection.Id);
        StatusMessage = $"选中连接: {connection.Id}";
    }
    
    /// <summary>
    /// 删除连接
    /// </summary>
    private void DeleteConnection(SFCConnectionViewModel connectionViewModel)
    {
        var connection = Connections.FirstOrDefault(c => c.Id == connectionViewModel.Id);
        if (connection != null)
        {
            // 如果连接涉及分支，需要更新分支的输入/输出列表
            UpdateBranchConnectionsOnDelete(connection);
            
            // 删除模型和视图模型
            Connections.Remove(connection);
            CurrentSFC.Connections.Remove(connection);
            ConnectionViewModels.Remove(connectionViewModel);
            
            StatusMessage = $"删除了连接: {connectionViewModel.Id}";
            IsModified = true;
        }
    }
    
    /// <summary>
    /// 当删除连接时更新分支的输入/输出列表
    /// </summary>
    private void UpdateBranchConnectionsOnDelete(SFCConnectionModel connection)
    {
        // 检查源是否为分支
        var sourceBranch = Branches.FirstOrDefault(b => b.Id == connection.SourceId);
        if (sourceBranch != null && sourceBranch.OutputStepIds.Contains(connection.TargetId))
        {
            sourceBranch.OutputStepIds.Remove(connection.TargetId);
        }
        
        // 检查目标是否为分支
        var targetBranch = Branches.FirstOrDefault(b => b.Id == connection.TargetId);
        if (targetBranch != null && targetBranch.InputStepIds.Contains(connection.SourceId))
        {
            targetBranch.InputStepIds.Remove(connection.SourceId);
        }
    }
    
    /// <summary>
    /// 获取元素ID
    /// </summary>
    private string GetElementId(object element)
    {
        return element switch
        {
            SFCStepModel step => step.Id,
            SFCTransitionModel transition => transition.Id,
            SFCBranchModel branch => branch.Id,
            SFCStepViewModel stepVM => stepVM.Id,
            SFCTransitionViewModel transitionVM => transitionVM.Id,
            SFCBranchViewModel branchVM => branchVM.Id,
            SFCConnectionViewModel connVM => connVM.Id,
            _ => ""
        };
    }

    /// <summary>
    /// 获取元素名称
    /// </summary>
    private string GetElementName(object element)
    {
        return element switch
        {
            SFCStepModel step => step.Name,
            SFCTransitionModel transition => transition.Name,
            SFCBranchModel branch => branch.Name,
            SFCStepViewModel stepVM => stepVM.Name,
            SFCTransitionViewModel transitionVM => transitionVM.Name,
            SFCBranchViewModel branchVM => branchVM.Name,
            SFCConnectionViewModel connVM => connVM.Id,
            _ => "未知元素"
        };
    }

    /// <summary>
    /// 获取元素类型
    /// </summary>
    private SFCElementType GetElementType(object element)
    {
        return element switch
        {
            SFCStepModel => SFCElementType.Step,
            SFCTransitionModel => SFCElementType.Transition,
            SFCBranchModel => SFCElementType.Branch,
            SFCStepViewModel => SFCElementType.Step,
            SFCTransitionViewModel => SFCElementType.Transition,
            SFCBranchViewModel => SFCElementType.Branch,
            _ => SFCElementType.Step
        };
    }

    private void ValidateSFC()
    {
        try
        {
            ValidationResult = _validator.ValidateSFC(CurrentSFC);
            StatusMessage = ValidationResult.IsValid ? "验证通过" : $"验证失败: {ValidationResult.Errors.Count} 个错误";
        }
        catch (Exception ex)
        {
            StatusMessage = $"验证出错: {ex.Message}";
        }
    }

    private void GenerateCode()
    {
        try
        {
            GeneratedCode = _codeGenerator.GenerateIECCode(CurrentSFC);
            StatusMessage = "代码生成完成";
        }
        catch (Exception ex)
        {
            StatusMessage = $"代码生成出错: {ex.Message}";
        }
    }

    private void SaveSFC()
    {
        // TODO: 实现保存功能
        StatusMessage = "保存功能待实现";
    }

    private void LoadSFC()
    {
        // TODO: 实现加载功能
        StatusMessage = "加载功能待实现";
    }

    private void NewSFC()
    {
        InitializeSampleData();
        IsModified = false;
        StatusMessage = "🆕 已创建新SFC流程 (仅含初始步)";
    }

    private void ExportCode()
    {
        if (string.IsNullOrEmpty(GeneratedCode))
        {
            GenerateCode();
        }
        
        // TODO: 实现代码导出功能
        StatusMessage = "代码导出功能待实现";
    }

    private void ClearSelection()
    {
        SelectedElement = null;
        IsConnecting = false;
        ConnectionSource = null;
        StatusMessage = "清除了选择";
    }

    /// <summary>
    /// 删除当前选中的元素
    /// </summary>
    private void DeleteSelectedElement()
    {
        System.Diagnostics.Debug.WriteLine($"[DeleteSelectedElement] 开始执行，SelectedElement: {SelectedElement?.GetType().Name}");

        if (SelectedElement == null)
        {
            StatusMessage = "没有选中的元素可删除";
            System.Diagnostics.Debug.WriteLine("[DeleteSelectedElement] 没有选中的元素");
            return;
        }

        System.Diagnostics.Debug.WriteLine($"[DeleteSelectedElement] 准备删除元素: {SelectedElement}");

        // 直接调用现有的DeleteElement方法
        DeleteElement(SelectedElement);

        // 清除选择状态
        SelectedElement = null;
        StatusMessage = "已删除选中的元素";
        System.Diagnostics.Debug.WriteLine("[DeleteSelectedElement] 删除完成");
    }

    /// <summary>
    /// 检查是否可以删除当前选中的元素
    /// </summary>
    /// <returns>如果有选中元素且可以删除则返回true</returns>
    private bool CanDeleteSelectedElement()
    {
        return SelectedElement != null;
    }

    private void OnDataChanged()
    {
        IsModified = true;
        CurrentSFC.ModifiedTime = DateTime.Now;
    }

    private void InitializeSampleData()
    {
        // 清空现有集合，确保仅包含一个初始步
        Steps.Clear();
        Transitions.Clear();
        Branches.Clear();
        Connections.Clear();
        Actions.Clear();
        StepViewModels.Clear();
        TransitionViewModels.Clear();
        BranchViewModels.Clear();
        ConnectionViewModels.Clear();

        // 创建一个初始步骤
        var initialStep = CreateNewStep(new Point(200, 200));
        initialStep.Name = "初始步";
        initialStep.IsInitialStep = true;
        AddStepToCollections(initialStep);

        SelectedElement = StepViewModels.FirstOrDefault();
        StatusMessage = "已创建新的SFC流程(含一个初始步)";
    }

    public void AddStepToCollections(SFCStepModel step)
    {
        Steps.Add(step);
        CurrentSFC.Steps.Add(step);
        
        var stepViewModel = new SFCStepViewModel
        {
            Id = step.Id,
            Name = step.Name,
            Position = step.Position,
            StepNumber = step.StepNumber,
            IsInitialStep = step.IsInitialStep,
            Description = step.Description
        };
        
        // 设置命令
        stepViewModel.EditPropertiesCommand = new RelayCommand(() => EditStep(step));
        
        StepViewModels.Add(stepViewModel);
    }

    private void AddTransitionToCollections(SFCTransitionModel transition)
    {
        Transitions.Add(transition);
        CurrentSFC.Transitions.Add(transition);

        var transitionViewModel = new SFCTransitionViewModel
        {
            Id = transition.Id,
            Name = transition.Name,
            Position = transition.Position,
            TransitionNumber = transition.TransitionNumber,
            ConditionExpression = transition.ConditionExpression ?? "TRUE",
            Description = transition.Description
        };

        System.Diagnostics.Debug.WriteLine($"[AddTransitionToCollections] Model位置: ({transition.Position.X}, {transition.Position.Y})");
        System.Diagnostics.Debug.WriteLine($"[AddTransitionToCollections] ViewModel位置: ({transitionViewModel.Position.X}, {transitionViewModel.Position.Y})");

        // 设置命令
        transitionViewModel.EditPropertiesCommand = new RelayCommand(() => EditTransition(transition));

        TransitionViewModels.Add(transitionViewModel);
    }

    #region 调试功能

    private void InitializeDebugger()
    {
        // 订阅调试器事件
        _debugger.StepChanged += OnDebugStepChanged;
        _debugger.BreakpointHit += OnBreakpointHit;
        _debugger.MessageLogged += OnDebugMessageLogged;
        _debugger.PropertyChanged += OnDebuggerPropertyChanged;
    }

    private async Task StartDebugging()
    {
        try
        {
            if (await _debugger.StartDebuggingAsync(CurrentSFC))
            {
                StatusMessage = "调试已启动";
                OnPropertyChanged(nameof(IsDebugging));
                OnPropertyChanged(nameof(IsPaused));
            }
            else
            {
                StatusMessage = "启动调试失败";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"启动调试失败: {ex.Message}";
        }
    }

    private async Task StopDebugging()
    {
        try
        {
            await _debugger.StopDebuggingAsync();
            StatusMessage = "调试已停止";
            OnPropertyChanged(nameof(IsDebugging));
            OnPropertyChanged(nameof(IsPaused));
        }
        catch (Exception ex)
        {
            StatusMessage = $"停止调试失败: {ex.Message}";
        }
    }

    private void AddBreakpoint(SFCStepModel? step)
    {
        if (step != null)
        {
            _debugger.AddBreakpoint(step);
            StatusMessage = $"在步骤 '{step.Name}' 添加断点";
        }
    }

    private void RemoveBreakpoint(SFCStepModel? step)
    {
        if (step != null)
        {
            _debugger.RemoveBreakpoint(step);
            StatusMessage = $"移除步骤 '{step.Name}' 的断点";
        }
    }

    private void AddWatchVariable(string? variableName)
    {
        if (!string.IsNullOrEmpty(variableName))
        {
            _debugger.AddWatchVariable(variableName);
            StatusMessage = $"添加监视变量: {variableName}";
        }
    }

    private async Task CompileWithMatIEC()
    {
        try
        {
            StatusMessage = "正在使用MatIEC编译...";

            var result = await _compilerIntegration.CompileSFCAsync(CurrentSFC);

            if (result.Success)
            {
                StatusMessage = "MatIEC编译成功";
                GeneratedCode = result.GeneratedCode;
            }
            else
            {
                StatusMessage = $"MatIEC编译失败: {result.ErrorMessage}";
            }

            ValidationResult = result.InternalValidation;
        }
        catch (Exception ex)
        {
            StatusMessage = $"MatIEC编译出错: {ex.Message}";
        }
    }

    private void OnDebugStepChanged(object? sender, SFCStepChangedEventArgs e)
    {
        OnPropertyChanged(nameof(CurrentDebugStep));
        StatusMessage = $"当前步骤: {e.CurrentStep.Name}";
    }

    private void OnBreakpointHit(object? sender, SFCBreakpointHitEventArgs e)
    {
        StatusMessage = $"断点命中: {e.Step.Name}";
    }

    private void OnDebugMessageLogged(object? sender, SFCDebugMessageEventArgs e)
    {
        // 可以在这里处理调试消息，例如显示在日志面板中
    }

    private void OnDebuggerPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        // 转发调试器的属性变化通知
        switch (e.PropertyName)
        {
            case nameof(SFCDebuggerService.IsDebugging):
                OnPropertyChanged(nameof(IsDebugging));
                break;
            case nameof(SFCDebuggerService.IsPaused):
                OnPropertyChanged(nameof(IsPaused));
                break;
            case nameof(SFCDebuggerService.CurrentStep):
                OnPropertyChanged(nameof(CurrentDebugStep));
                break;
        }
    }

    #endregion

    /// <summary>
    /// 更新步骤属性
    /// </summary>
    public void UpdateStep(SFCStepModel step)
    {
        if (step == null) return;
        
        // 查找对应的步骤视图模型并更新
        var stepViewModel = StepViewModels.FirstOrDefault(vm => vm.Id == step.Id);
        if (stepViewModel != null)
        {
            // 更新视图模型的关键属性
            stepViewModel.Name = step.Name;
            stepViewModel.Description = step.Description;
            stepViewModel.Position = step.Position;
            stepViewModel.IsInitialStep = step.IsInitialStep;
            // 其他属性更新...
        }
        
        // 更新模型列表中的步骤
        var modelIndex = Steps.IndexOf(step);
        if (modelIndex >= 0)
        {
            Steps[modelIndex] = step;
        }
        
        // 同步到当前SFC模型
        var currentModelIndex = CurrentSFC.Steps.FindIndex(s => s.Id == step.Id);
        if (currentModelIndex >= 0)
        {
            CurrentSFC.Steps[currentModelIndex] = step;
        }
        
        // 更新修改标志
        IsModified = true;
        StatusMessage = $"更新了步骤: {step.Name}";
    }
    
    /// <summary>
    /// 更新转换属性
    /// </summary>
    public void UpdateTransition(SFCTransitionModel transition)
    {
        if (transition == null) return;
        
        // 查找对应的转换视图模型并更新
        var transitionViewModel = TransitionViewModels.FirstOrDefault(vm => vm.Id == transition.Id);
        if (transitionViewModel != null)
        {
            // 更新视图模型的关键属性
            transitionViewModel.Name = transition.Name;
            transitionViewModel.Description = transition.Description;
            transitionViewModel.Position = transition.Position;
            transitionViewModel.ConditionExpression = transition.ConditionExpression;
            transitionViewModel.IsAlwaysTrue = transition.IsAlwaysTrue;
            // 其他属性更新...
        }
        
        // 更新模型列表中的转换
        var modelIndex = Transitions.IndexOf(transition);
        if (modelIndex >= 0)
        {
            Transitions[modelIndex] = transition;
        }
        
        // 同步到当前SFC模型
        var currentModelIndex = CurrentSFC.Transitions.FindIndex(t => t.Id == transition.Id);
        if (currentModelIndex >= 0)
        {
            CurrentSFC.Transitions[currentModelIndex] = transition;
        }
        
        // 更新修改标志
        IsModified = true;
        StatusMessage = $"更新了转换: {transition.Name}";
    }
    
    /// <summary>
    /// 编辑步骤属性
    /// </summary>
    public void EditStep(SFCStepModel step)
    {
        if (step == null) return;
        
        // 创建步骤编辑器视图模型
        var stepEditorViewModel = new SFCStepEditorViewModel(step, this);
        
        // 创建并显示编辑对话框
        var stepEditorView = new Views.SFCStepEditorView
        {
            DataContext = stepEditorViewModel
        };
        
        var window = new Window
        {
            Title = "编辑步骤属性",
            Content = stepEditorView,
            SizeToContent = SizeToContent.WidthAndHeight,
            WindowStartupLocation = WindowStartupLocation.CenterOwner,
            Owner = Application.Current.MainWindow,
            ResizeMode = ResizeMode.NoResize
        };
        
        // 处理取消按钮事件
        stepEditorViewModel.CancelCommand = new RelayCommand(() => window.Close());
        
        // 处理保存按钮事件
        stepEditorViewModel.SaveCommand = new RelayCommand(() => 
        {
            UpdateStep(step);
            window.Close();
        });
        
        window.ShowDialog();
        StatusMessage = "步骤编辑完成";
    }
    
    /// <summary>
    /// 编辑转换属性
    /// </summary>
    public void EditTransition(SFCTransitionModel transition)
    {
        if (transition == null) return;
        
        // 创建转换编辑器视图模型
        var transitionEditorViewModel = new SFCTransitionEditorViewModel(transition, this);
        
        // 创建并显示编辑对话框
        var transitionEditorView = new Views.SFCTransitionEditorView
        {
            DataContext = transitionEditorViewModel
        };
        
        var window = new Window
        {
            Title = "编辑转换属性",
            Content = transitionEditorView,
            SizeToContent = SizeToContent.WidthAndHeight,
            WindowStartupLocation = WindowStartupLocation.CenterOwner,
            Owner = Application.Current.MainWindow,
            ResizeMode = ResizeMode.NoResize
        };
        
        // 处理取消按钮事件
        transitionEditorViewModel.CancelCommand = new RelayCommand(() => window.Close());
        
        // 处理保存按钮事件
        transitionEditorViewModel.SaveCommand = new RelayCommand(() => 
        {
            UpdateTransition(transition);
            window.Close();
        });
        
        window.ShowDialog();
        StatusMessage = "转换编辑完成";
    }

    /// <summary>
    /// 在选中元素后插入步骤（西门子Graph风格）
    /// </summary>
    public void InsertStepAfterSelected()
    {
        // 检查是否可以插入步骤
        bool canInsertStep = false;
        string selectedName = "";

        if (SelectedElement != null)
        {
            var elementType = GetElementType(SelectedElement);

            // 情况1：选中的是转换条件
            if (elementType == SFCElementType.Transition)
            {
                canInsertStep = true;
                selectedName = GetElementName(SelectedElement);
            }
            // 情况2：选中的是选择分支的右侧部分（转换条件部分）
            else if (elementType == SFCElementType.Branch && SelectedElement is SFCBranchViewModel branchVM)
            {
                if (branchVM.BranchType == SFCBranchType.Selection && branchVM.SelectedPart == "Right")
                {
                    canInsertStep = true;
                    selectedName = $"{branchVM.Name}(右侧转换条件)";
                }
            }
        }

        if (!canInsertStep)
        {
            MessageBox.Show("西门子Graph规则：步骤只能插入在转换条件之后。\n请先选择一个转换条件或选择分支的右侧转换条件部分，然后再插入步骤。",
                "Graph规则提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }
        
        // 获取选中元素的信息（使用已有的selectedName变量）
        System.Diagnostics.Debug.WriteLine($"[InsertStepAfterSelected] 选中元素: {selectedName}");
        
        // 获取选中元素的位置
        Point position = GetElementPosition(SelectedElement);
        System.Diagnostics.Debug.WriteLine($"[InsertStepAfterSelected] 选中元素位置: {position}");

        // 计算步骤的位置
        Point stepPosition;
        Size stepSize = STANDARD_STEP_SIZE;

        if (SelectedElement is SFCBranchViewModel branchVM2 && branchVM2.BranchType == SFCBranchType.Selection && branchVM2.SelectedPart == "Right")
        {
            // 选择分支右侧部分的情况 - 使用动态计算方法，现在索引3会正确返回T1转换条件的下连接点
            stepPosition = CalculateConnectPointAlignedPositionDynamic(SelectedElement, stepSize, 3);
            System.Diagnostics.Debug.WriteLine($"[InsertStepAfterSelected] 选择分支右侧使用动态计算: 连接点索引3, 步骤位置{stepPosition}");
        }
        else
        {
            // 普通转换条件的情况 - 使用动态连接点对齐计算
            stepPosition = CalculateConnectPointAlignedPositionDynamic(SelectedElement, stepSize);
        }
        
        System.Diagnostics.Debug.WriteLine($"[InsertStepAfterSelected] 新步骤位置: {stepPosition}");
        
        // 使用精确位置创建新步骤
        SFCStepModel step = CreateNewStep(stepPosition);
        System.Diagnostics.Debug.WriteLine($"[InsertStepAfterSelected] 新步骤创建: {step.Id}");
        
        // 添加步骤到集合
        AddStepToCollections(step);
        System.Diagnostics.Debug.WriteLine($"[InsertStepAfterSelected] 步骤已添加到集合");
        
        // 西门子Graph风格：自动创建垂直连接
        System.Diagnostics.Debug.WriteLine($"[InsertStepAfterSelected] 准备创建连接: {GetElementId(SelectedElement)} -> {step.Id}");

        // 检查是否需要创建连接线
        bool shouldCreateConnection = true;
        int sourceConnectPointIndex = 0;

        // 选择分支右侧部分需要特殊处理：使用索引3连接点（现在会正确返回T1转换条件的下连接点）
        string actualSourceId = GetElementId(SelectedElement);
        if (SelectedElement is SFCBranchViewModel branchVM3 && branchVM3.BranchType == SFCBranchType.Selection && branchVM3.SelectedPart == "Right")
        {
            // 对于选择分支右侧，使用分支的索引3连接点（现在会正确返回T1转换条件的下连接点）
            actualSourceId = branchVM3.Id;
            sourceConnectPointIndex = 3; // 选择分支右侧T1转换条件下连接点
            System.Diagnostics.Debug.WriteLine($"[InsertStepAfterSelected] 使用选择分支右侧T1连接点: {branchVM3.Id}[索引3]");
        }

        if (shouldCreateConnection)
        {
            System.Diagnostics.Debug.WriteLine($"[InsertStepAfterSelected] 开始创建连接: {actualSourceId} -> {step.Id}, 源索引: {sourceConnectPointIndex}");
            var connection = AddConnection(actualSourceId, step.Id, sourceConnectPointIndex, 0);
            if (connection != null)
            {
                System.Diagnostics.Debug.WriteLine($"[InsertStepAfterSelected] ✅ 连接创建成功: {connection.Id}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[InsertStepAfterSelected] ❌ 连接创建失败");
            }
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"[InsertStepAfterSelected] 跳过连接创建");
        }

        // 再次确保垂直对齐（以防其他因素影响）
        if (!(SelectedElement is SFCBranchViewModel branchVM4 && branchVM4.SelectedPart == "Right"))
        {
            // 只对普通转换条件进行垂直对齐，选择分支右侧部分不需要
            AlignElementVertically(SelectedElement, step);
        }
        
        // 自动选中新创建的步骤（方便后续操作）
        SelectedElement = StepViewModels.FirstOrDefault(s => s.Id == step.Id);
        
        StatusMessage = $"✅ 已按西门子Graph风格在[{selectedName}]后插入步骤";
    }

    /// <summary>
    /// 在选中元素后插入转换条件（西门子Graph风格）
    /// </summary>
    public void InsertTransitionAfterSelected()
    {
        // 检查是否可以插入转换条件
        bool canInsertTransition = false;
        string selectedName = "";

        if (SelectedElement != null)
        {
            var elementType = GetElementType(SelectedElement);

            // 情况1：选中的是步骤
            if (elementType == SFCElementType.Step)
            {
                canInsertTransition = true;
                selectedName = GetElementName(SelectedElement);
            }
            // 情况2：选中的是选择分支的左侧部分
            else if (elementType == SFCElementType.Branch && SelectedElement is SFCBranchViewModel branchVM)
            {
                if (branchVM.BranchType == SFCBranchType.Selection && branchVM.SelectedPart == "Left")
                {
                    canInsertTransition = true;
                    selectedName = $"{branchVM.Name}(左侧)";
                }
            }
        }

        if (!canInsertTransition)
        {
            MessageBox.Show("西门子Graph规则：转换条件只能插入在步骤之后，或选择分支的左侧部分之后。\n请先选择一个步骤或选择分支的左侧部分，然后再插入转换条件。",
                "Graph规则提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }
        
        // 简化调试输出 - 只保留关键信息
        // System.Diagnostics.Debug.WriteLine($"[InsertTransitionAfterSelected] 开始执行");

        // 获取选中元素的信息
        selectedName = GetElementName(SelectedElement);
        // System.Diagnostics.Debug.WriteLine($"[InsertTransitionAfterSelected] 选中元素: {selectedName}");

        // 获取选中元素的位置
        Point position = GetElementPosition(SelectedElement);
        Size selectedElementSize = GetElementSize(SelectedElement);
        // System.Diagnostics.Debug.WriteLine($"[InsertTransitionAfterSelected] 选中元素位置: {position}");
        // System.Diagnostics.Debug.WriteLine($"[InsertTransitionAfterSelected] 选中元素尺寸: {selectedElementSize}");
        // System.Diagnostics.Debug.WriteLine($"[InsertTransitionAfterSelected] 选中元素类型: {SelectedElement?.GetType().Name}");

        // 计算转换条件的位置
        Point transitionPosition;
        Size transitionSize = STANDARD_TRANSITION_SIZE;

        if (SelectedElement is SFCBranchViewModel branchVM2 && branchVM2.BranchType == SFCBranchType.Selection && branchVM2.SelectedPart == "Left")
        {
            // 选择分支左侧部分的情况
            // 使用与拖拽时完全相同的动态计算方法
            // 这样确保静态计算和动态计算完全一致，且能自动适应元素变化

            // 尝试获取实际连接点的位置（动态方法）
            var leftConnectPoint = GetActualConnectPointPosition(branchVM2, 1);
            if (leftConnectPoint.HasValue)
            {
                // 使用实际连接点位置
                double alignedX = leftConnectPoint.Value.X - transitionSize.Width / 2;
                double transitionY = position.Y + 35; // 与右侧转换条件同一高度
                transitionPosition = new Point(alignedX, transitionY);

                System.Diagnostics.Debug.WriteLine($"[InsertTransitionAfterSelected] 动态获取左侧连接点位置: {leftConnectPoint.Value}, 转换条件X={alignedX}");
            }
            else
            {
                // 回退到计算方法（如果无法获取实际位置）
                var calculatedPoint = CalculateElementConnectPoint(branchVM2, position, false, 1);
                double alignedX = calculatedPoint.X - transitionSize.Width / 2;
                double transitionY = position.Y + 35;
                transitionPosition = new Point(alignedX, transitionY);

                System.Diagnostics.Debug.WriteLine($"[InsertTransitionAfterSelected] 回退计算左侧连接点位置: {calculatedPoint}, 转换条件X={alignedX}");
            }
        }
        else
        {
            // 步骤的情况 - 使用动态连接点对齐计算
            transitionPosition = CalculateConnectPointAlignedPositionDynamic(SelectedElement, transitionSize);
        }
        
        // System.Diagnostics.Debug.WriteLine($"[InsertTransitionAfterSelected] 新转换条件位置: {transitionPosition}");

        // 使用精确位置创建新转换条件
        SFCTransitionModel transition = CreateNewTransition(transitionPosition);
        // System.Diagnostics.Debug.WriteLine($"[InsertTransitionAfterSelected] 新转换条件创建: {transition.Id}");

        // 添加转换条件到集合
        AddTransitionToCollections(transition);
        // System.Diagnostics.Debug.WriteLine($"[InsertTransitionAfterSelected] 转换条件已添加到集合");

        // 西门子Graph风格：自动创建垂直连接
        System.Diagnostics.Debug.WriteLine($"[连接创建] 准备创建连接: {GetElementId(SelectedElement)} -> {transition.Id}");

        // 根据选中元素类型确定连接点索引
        int sourceConnectPointIndex = 0;
        if (SelectedElement is SFCBranchViewModel branchVM3 && branchVM3.BranchType == SFCBranchType.Selection && branchVM3.SelectedPart == "Left")
        {
            // 选择分支左侧部分使用左侧连接点（索引1）
            sourceConnectPointIndex = 1;
        }

        AddConnection(GetElementId(SelectedElement), transition.Id, sourceConnectPointIndex, 0);

        // 再次确保垂直对齐（以防其他因素影响）
        if (!(SelectedElement is SFCBranchViewModel branchVM4 && branchVM4.SelectedPart == "Left"))
        {
            // 只对步骤进行垂直对齐，选择分支左侧部分不需要
            AlignElementVertically(SelectedElement, transition);
        }
        
        // 自动选中新创建的转换条件（方便后续操作）
        SelectedElement = TransitionViewModels.FirstOrDefault(t => t.Id == transition.Id);

        StatusMessage = $"✅ 已按西门子Graph风格在[{selectedName}]后插入转换条件";
    }

    /// <summary>
    /// 在选中元素后插入顺控器终止
    /// </summary>
    public void InsertEndAfterSelected()
    {
        // 仅允许在"转换条件"后插入顺控器终止（规则6.3）
        if (SelectedElement == null || GetElementType(SelectedElement) != SFCElementType.Transition)
        {
            MessageBox.Show("请选择一个转换条件后再插入顺控器终止。", "操作提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        if (SelectedElement == null)
        {
            MessageBox.Show("请先选择一个元素", "操作提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        // 获取选中元素的位置和类型
        Point position = GetElementPosition(SelectedElement);
        SFCElementType elementType = GetElementType(SelectedElement);
        
        // 获取选中元素的尺寸
        Size selectedSize = GetElementSize(SelectedElement);
        
        // 计算选中元素的中心X坐标
        double selectedCenterX = position.X + selectedSize.Width / 2;
        
        // 使用标准步骤尺寸
        Size stepSize = STANDARD_STEP_SIZE;
        
        // 使用动态方法计算对齐后的结尾步骤位置
        Point newPosition;
        var sourceConnectPoint = GetActualConnectPointPosition(SelectedElement, 0, true); // 获取输出连接点
        if (sourceConnectPoint.HasValue)
        {
            double alignedX = sourceConnectPoint.Value.X - stepSize.Width / 2;
            newPosition = new Point(alignedX, sourceConnectPoint.Value.Y + 80);
            System.Diagnostics.Debug.WriteLine($"[InsertEndAfterSelected] 动态计算结尾步骤位置: 连接点{sourceConnectPoint.Value}, 步骤位置{newPosition}");
        }
        else
        {
            // 回退到原有计算
            double alignedX = selectedCenterX - stepSize.Width / 2;
            newPosition = new Point(alignedX, position.Y + 100);
            System.Diagnostics.Debug.WriteLine($"[InsertEndAfterSelected] 回退计算结尾步骤位置: {newPosition}");
        }
        
        // 创建新顺控器终止步骤
        SFCStepModel endStep = CreateNewStep(newPosition);
        endStep.StepType = SFCStepType.Normal;
        endStep.Name = "顺控器终止";
        // 添加特殊标识，用于区分终止元素
        endStep.Description = "TERMINATOR_ELEMENT";
        
        // 根据选中元素类型决定连接方式
        switch (elementType)
        {
            case SFCElementType.Step:
                // 步骤后必须先插入转换条件
                // 计算转换条件的对齐位置
                double transitionAlignedX = selectedCenterX - STANDARD_TRANSITION_SIZE.Width / 2;
                SFCTransitionModel transition = CreateNewTransition(new Point(transitionAlignedX, position.Y + 60));
                AddTransitionToCollections(transition);
                
                // 连接：选中步骤 -> 转换条件 -> 结尾步骤
                AddConnection(GetElementId(SelectedElement), transition.Id, 0, 0);
                AddConnection(transition.Id, endStep.Id, 0, 0);
                
                // 确保垂直对齐
                AlignElementVertically(SelectedElement, transition);
                AlignElementVertically(transition, endStep);
                break;
                
            case SFCElementType.Transition:
                // 转换条件后直接连接结尾步骤
                AddConnection(GetElementId(SelectedElement), endStep.Id, 0, 0);
                break;
                
            case SFCElementType.Branch:
                // 分支后直接连接结尾步骤
                AddConnection(GetElementId(SelectedElement), endStep.Id, 0, 0);
                break;
        }
        
        // 添加结尾步骤到集合
        AddStepToCollections(endStep);
        
        // 选中新创建的结尾步骤
        SelectedElement = StepViewModels.FirstOrDefault(s => s.Id == endStep.Id);
        
        StatusMessage = $"已在选中元素后插入顺控器终止";
    }

    /// <summary>
    /// 在选中元素后插入跳转
    /// </summary>
    public void InsertJumpAfterSelected(string targetStepId)
    {
        // 仅允许在"转换条件"后插入跳转（规则6.3）
        if (SelectedElement == null || GetElementType(SelectedElement) != SFCElementType.Transition)
        {
            MessageBox.Show("请选择一个转换条件后再插入跳转。", "操作提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        if (SelectedElement == null)
        {
            MessageBox.Show("请先选择一个元素", "操作提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        // 确保目标步骤存在
        var targetStep = Steps.FirstOrDefault(s => s.Id == targetStepId);
        if (targetStep == null)
        {
            MessageBox.Show("跳转目标步骤不存在", "操作错误", MessageBoxButton.OK, MessageBoxImage.Error);
            return;
        }

        // 获取选中元素的位置和类型
        Point position = GetElementPosition(SelectedElement);
        SFCElementType elementType = GetElementType(SelectedElement);
        
        // 获取选中元素的尺寸
        Size selectedSize = GetElementSize(SelectedElement);
        
        // 计算选中元素的中心X坐标
        double selectedCenterX = position.X + selectedSize.Width / 2;
        
        // 使用动态方法计算对齐后的转换条件位置
        Point transitionPosition = CalculateConnectPointAlignedPositionDynamic(SelectedElement, STANDARD_TRANSITION_SIZE);
        
        // 创建新转换条件
        SFCTransitionModel jumpTransition = CreateNewTransition(transitionPosition);
        jumpTransition.Name = $"跳转到{targetStep.Name}";
        
        // 根据选中元素类型决定连接方式
        switch (elementType)
        {
            case SFCElementType.Step:
                // 步骤后直接连接转换条件
                AddConnection(GetElementId(SelectedElement), jumpTransition.Id, 0, 0);
                break;
                
            case SFCElementType.Transition:
                // 转换条件后必须先插入步骤
                // 使用动态方法计算步骤的对齐位置
                Point stepPosition = CalculateConnectPointAlignedPositionDynamic(SelectedElement, STANDARD_STEP_SIZE);
                SFCStepModel step = CreateNewStep(stepPosition);
                AddStepToCollections(step);
                
                // 连接：选中转换条件 -> 步骤 -> 跳转转换条件
                AddConnection(GetElementId(SelectedElement), step.Id, 0, 0);
                AddConnection(step.Id, jumpTransition.Id, 0, 0);
                
                // 确保垂直对齐
                AlignElementVertically(SelectedElement, step);
                AlignElementVertically(step, jumpTransition);
                break;
                
            case SFCElementType.Branch:
                // 分支后必须先插入步骤
                // 使用动态方法计算步骤的对齐位置
                Point branchStepPosition = CalculateConnectPointAlignedPositionDynamic(SelectedElement, STANDARD_STEP_SIZE);
                SFCStepModel branchStep = CreateNewStep(branchStepPosition);
                AddStepToCollections(branchStep);
                
                // 连接：选中分支 -> 步骤 -> 跳转转换条件
                AddConnection(GetElementId(SelectedElement), branchStep.Id, 0, 0);
                AddConnection(branchStep.Id, jumpTransition.Id, 0, 0);
                
                // 确保垂直对齐
                AlignElementVertically(SelectedElement, branchStep);
                AlignElementVertically(branchStep, jumpTransition);
                break;
        }
        
        // 添加跳转转换条件到集合
        AddTransitionToCollections(jumpTransition);
        
        // 创建跳转连接
        var jumpConnection = new SFCConnectionModel
        {
            Id = Guid.NewGuid().ToString(),
            SourceId = jumpTransition.Id,
            TargetId = targetStep.Id,
            SourceType = SFCElementType.Transition,
            TargetType = SFCElementType.Step,
            ConnectionType = SFCConnectionType.Jump,
            Label = targetStep.Name
        };
        
        // 添加跳转连接
        Connections.Add(jumpConnection);
        AddConnectionViewModel(jumpConnection);
        
        // 选中新创建的跳转转换条件
        SelectedElement = TransitionViewModels.FirstOrDefault(t => t.Id == jumpTransition.Id);
        
        StatusMessage = $"已在选中元素后插入跳转到 {targetStep.Name}";
    }

    /// <summary>
    /// 在选中元素后插入选择分支（西门子Graph风格）
    /// </summary>
    public void InsertSelectionBranchAfterSelected()
    {
        if (SelectedElement == null)
        {
            MessageBox.Show("请先选择一个元素，然后再插入选择分支。",
                "Graph规则提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        var elementType = GetElementType(SelectedElement);

        // 情况1：在步骤后插入选择分支（标准情况）
        if (elementType == SFCElementType.Step)
        {
            InsertSelectionBranchAfterStep();
            return;
        }

        // 情况2：在现有选择分支后插入新的选择分支（多分支情况）
        if (elementType == SFCElementType.Branch && SelectedElement is SFCBranchViewModel branchViewModel)
        {
            if (branchViewModel.BranchType == SFCBranchType.Selection)
            {
                // 使用多分支插入功能
                InsertSelectionBranchFromAnyPart();
                return;
            }
        }

        // 其他情况不允许插入
        MessageBox.Show("西门子Graph规则：选择分支只能插入在步骤之后，或在现有选择分支后添加多分支。\n请先选择一个步骤或选择分支，然后再插入选择分支。",
            "Graph规则提示", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    /// <summary>
    /// 在步骤后插入选择分支的具体实现
    /// </summary>
    private void InsertSelectionBranchAfterStep()
    {
        // 获取选中元素的信息
        string selectedName = GetElementName(SelectedElement);
        System.Diagnostics.Debug.WriteLine($"[InsertSelectionBranchAfterStep] 选中元素: {selectedName}");

        // === 先确定分支类型（Initial 或 Subsequent） ===
        string stepId = GetElementId(SelectedElement);
        var existingBranches = Branches
            .Where(b => b.BranchType == SFCBranchType.Selection)
            .Where(b => Connections.Any(c => c.SourceId == stepId && c.TargetId == b.Id))
            .ToList();

        bool isFirstBranch = !existingBranches.Any();
        System.Diagnostics.Debug.WriteLine($"[选择分支] {(isFirstBranch ? "首次" : "后续")}插入，现有分支: {existingBranches.Count}个");

        // === 计算分支位置 ===
        Point branchPosition;
        if (isFirstBranch)
        {
            // 第一个分支：基于步骤位置计算
            Point position = GetElementPosition(SelectedElement);
            Size stepSize = GetElementSize(SelectedElement);
            double stepCenterX = position.X + stepSize.Width / 2;
            double stepBottomY = position.Y + stepSize.Height;

            double branchX = stepCenterX - SELECTION_BRANCH_OFFSET_X;
            double branchY = stepBottomY + SELECTION_BRANCH_OFFSET_Y;
            branchPosition = new Point(branchX, branchY);
        }
        else
        {
            // 后续分支：基于现有分支链的最右端计算，使用与InsertSiblingBranch相同的紧凑连接逻辑
            var rightmostBranch = FindRightmostBranchInChain(existingBranches.First());

            // 使用横线端点连接的位置计算公式（与InsertSiblingBranch保持一致）
            double rightOffset = rightmostBranch.ViewType == BranchViewType.Initial ?
                HORIZONTAL_LINE_RIGHT_OFFSET_INITIAL : HORIZONTAL_LINE_RIGHT_OFFSET_SUBSEQUENT;
            double prevBranchHorizontalLineRightX = rightmostBranch.Position.X + SELECTION_BRANCH_RIGHT_PART_LEFT + rightOffset;

            // 新分支位置：让新分支的横线左端与前一个分支的横线右端紧凑连接
            double newBranchX = prevBranchHorizontalLineRightX + BRANCH_CONNECTION_GAP - SELECTION_BRANCH_RIGHT_PART_LEFT - HORIZONTAL_LINE_LEFT_OFFSET;
            branchPosition = new Point(newBranchX, rightmostBranch.Position.Y);
        }

        System.Diagnostics.Debug.WriteLine($"[选择分支] 新分支位置: {branchPosition}");

        // 创建选择分支
        SFCBranchModel branch = CreateNewBranch(branchPosition, SFCBranchType.Selection);
        branch.ViewType = isFirstBranch ? BranchViewType.Initial : BranchViewType.Subsequent;

        // 确保位置正确设置
        branch.Position = branchPosition;
        System.Diagnostics.Debug.WriteLine($"[选择分支] 创建: {branch.ViewType}, 位置: {branch.Position}");

        // 添加分支到集合
        AddBranchToCollections(branch);

        // 建立连接关系
        if (isFirstBranch)
        {
            // 第一个分支：直接连接步骤到分支
            AddConnection(GetElementId(SelectedElement), branch.Id, 0, 0);
        }
        else
        {
            // 后续分支：连接到分支链末端
            var rightmostBranch = FindRightmostBranchInChain(existingBranches.First());
            rightmostBranch.NextBranchId = branch.Id;

            // 同步更新对应的ViewModel
            var rightmostBranchViewModel = BranchViewModels.FirstOrDefault(vm => vm.Id == rightmostBranch.Id);
            if (rightmostBranchViewModel != null)
            {
                rightmostBranchViewModel.NextBranchId = branch.Id;
            }
        }

        // 对于选择分支，不进行垂直对齐，因为位置已经精确计算
        // AlignElementVertically(SelectedElement, branch); // 注释掉，避免重置位置
        System.Diagnostics.Debug.WriteLine($"[选择分支] 跳过垂直对齐，保持计算位置: {branch.Position}");

        // === 西门子Graph风格：自动创建分支路径 ===
        if (isFirstBranch)
        {
            // 第一个分支：创建完整的分支结构
            // 注意：选择分支的实际尺寸由SFCSelectionBranchView.xaml中的Canvas定义
            // 这里不再使用硬编码的分支尺寸，保持选择分支内部结构的完整性

            // 第一个分支路径（左）- 西门子博图Graph风格：使用竖直连接线，不创建转换条件
            // 左侧路径现在由分支视图中的竖直连接线表示，不需要创建额外的转换条件
            System.Diagnostics.Debug.WriteLine($"[选择分支] 左分支使用竖直连接线，无需创建转换条件");

            // 第二个分支路径（右）- 西门子博图Graph风格：转换条件已内嵌在选择分支视图中
            // 选择分支视图本身包含完整的转换条件样式，无需创建独立的转换条件模型
            System.Diagnostics.Debug.WriteLine($"[选择分支] 右分支转换条件已内嵌在选择分支视图中，无需单独创建");
        }
        else
        {
            // 后续分支：只创建简单的分支，不需要额外的路径结构
            System.Diagnostics.Debug.WriteLine($"[选择分支] 后续分支，无需创建额外路径结构");
        }

        // 自动选中新创建的分支
        SelectedElement = BranchViewModels.FirstOrDefault(b => b.Id == branch.Id);

        StatusMessage = $"✅ 已按西门子Graph风格在步骤[{selectedName}]后插入选择分支";
    }

    /// <summary>
    /// 在选中元素后插入并行分支（西门子Graph风格）
    /// </summary>
    public void InsertParallelBranchAfterSelected()
    {
        if (SelectedElement == null)
        {
            MessageBox.Show("请先选择一个元素，然后再插入并行分支。",
                "Graph规则提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        var elementType = GetElementType(SelectedElement);

        // 情况1：在转换条件后插入并行分支（标准情况）
        if (elementType == SFCElementType.Transition)
        {
            InsertParallelBranchAfterTransition();
            return;
        }

        // 情况2：在现有并行分支后插入新的并行分支（多分支情况）
        if (elementType == SFCElementType.Branch && SelectedElement is SFCBranchViewModel branchViewModel)
        {
            if (branchViewModel.BranchType == SFCBranchType.Parallel)
            {
                // 使用多分支插入功能
                InsertParallelBranchFromAnyPart();
                return;
            }
        }

        // 其他情况：不支持的元素类型
        MessageBox.Show("西门子Graph规则：并行分支只能插入在转换条件之后，或在现有并行分支后继续插入。\n请先选择一个转换条件或现有并行分支，然后再插入并行分支。",
            "Graph规则提示", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    /// <summary>
    /// 在转换条件后插入并行分支（标准情况）
    /// </summary>
    private void InsertParallelBranchAfterTransition()
    {
        // 获取选中元素的信息
        string selectedName = GetElementName(SelectedElement);
        System.Diagnostics.Debug.WriteLine($"[InsertParallelBranchAfterTransition] 选中元素: {selectedName}");

        // === 先确定分支类型（Initial 或 Subsequent） ===
        string transitionId = GetElementId(SelectedElement);
        var existingBranches = Branches
            .Where(b => b.BranchType == SFCBranchType.Parallel)
            .Where(b => Connections.Any(c => c.SourceId == transitionId && c.TargetId == b.Id))
            .ToList();

        bool isFirstBranch = !existingBranches.Any();
        System.Diagnostics.Debug.WriteLine($"[并行分支] {(isFirstBranch ? "首次" : "后续")}插入，现有分支: {existingBranches.Count}个");

        // === 计算分支位置 ===
        Point branchPosition;
        if (isFirstBranch)
        {
            // 第一个分支：使用动态方法基于转换条件的连接点位置计算
            var transitionConnectPoint = GetActualConnectPointPosition(SelectedElement, 0, true); // 索引0：转换条件输出连接点
            if (transitionConnectPoint.HasValue)
            {
                Size branchSize = new Size(50, 30); // 默认分支尺寸
                double alignedBranchX = transitionConnectPoint.Value.X - branchSize.Width / 2 - 16;
                branchPosition = new Point(alignedBranchX, transitionConnectPoint.Value.Y + 14);
                System.Diagnostics.Debug.WriteLine($"[InsertParallelBranchAfterTransition] 动态计算首个并行分支位置: 连接点{transitionConnectPoint.Value}, 分支位置{branchPosition}");
            }
            else
            {
                // 回退到原有计算方法
                Point transitionPosition = GetElementPosition(SelectedElement);
                Size transitionSize = GetElementSize(SelectedElement);
                double transitionCenterX = transitionPosition.X + transitionSize.Width / 2;
                Size branchSize = new Size(50, 30);
                double alignedBranchX = transitionCenterX - branchSize.Width / 2 - 16;
                branchPosition = new Point(alignedBranchX, transitionPosition.Y + 34);
                System.Diagnostics.Debug.WriteLine($"[InsertParallelBranchAfterTransition] 回退计算首个并行分支位置: {branchPosition}");
            }
        }
        else
        {
            // 后续分支：使用统一的位置计算逻辑（与InsertSiblingParallelBranch保持一致）
            var rightmostBranch = FindRightmostBranchInChain(existingBranches.First());
            branchPosition = CalculateUnifiedParallelBranchPosition(BranchViewModels.FirstOrDefault(vm => vm.Id == rightmostBranch.Id));
        }

        System.Diagnostics.Debug.WriteLine($"[并行分支] 新分支位置: {branchPosition}");

        // 创建并行分支
        SFCBranchModel branch = CreateNewBranch(branchPosition, SFCBranchType.Parallel);
        branch.ViewType = isFirstBranch ? BranchViewType.Initial : BranchViewType.Subsequent;

        // 确保位置正确设置
        branch.Position = branchPosition;
        System.Diagnostics.Debug.WriteLine($"[并行分支] 创建: {branch.ViewType}, 位置: {branch.Position}");

        // 4. 先添加分支到集合（这样AddConnection才能找到目标对象）
        AddBranchToCollections(branch);

        // 5. 建立连接关系
        if (isFirstBranch)
        {
            // 第一个分支：直接连接转换条件 -> 并行分支
            AddConnection(GetElementId(SelectedElement), branch.Id, 0, 0);
        }
        else
        {
            // 后续分支：连接到分支链末端
            var rightmostBranch = FindRightmostBranchInChain(existingBranches.First());
            rightmostBranch.NextBranchId = branch.Id;

            // 同步更新对应的ViewModel
            var rightmostBranchViewModel = BranchViewModels.FirstOrDefault(vm => vm.Id == rightmostBranch.Id);
            if (rightmostBranchViewModel != null)
            {
                rightmostBranchViewModel.NextBranchId = branch.Id;
            }
        }

        // 6. 对于并行分支，不进行垂直对齐，因为位置已经精确计算
        // AlignElementVertically(SelectedElement, branch); // 注释掉，避免重置位置
        System.Diagnostics.Debug.WriteLine($"[并行分支] 跳过垂直对齐，保持计算位置: {branch.Position}");

        // === 西门子Graph风格：自动创建并行分支路径 ===

        if (isFirstBranch)
        {
            // 第一个分支：创建左右两侧的步骤
            // 第一个并行路径（左）- 西门子规则：创建一个步骤作为左分支路径
            Point leftPos = CalculateUnifiedParallelStepPosition(branchPosition, branch.Size, true);
            SFCStepModel leftStep = CreateNewStep(leftPos);
            leftStep.Name = "并行步骤";
            AddStepToCollections(leftStep);
            System.Diagnostics.Debug.WriteLine($"[InsertParallelBranchAfterTransition] 创建左分支步骤: {leftStep.Id}");

            // 使用左侧连接点创建连接
            System.Diagnostics.Debug.WriteLine($"[InsertParallelBranchAfterTransition] 创建左侧连接: {branch.Id} -> {leftStep.Id}, 源索引=1, 目标索引=0");
            AddConnection(branch.Id, leftStep.Id, 1, 0);

            // 第二个并行路径（右）- 西门子规则：自动添加一个步骤作为默认路径
            Point rightPos = CalculateUnifiedParallelStepPosition(branchPosition, branch.Size, false);
            SFCStepModel rightStep = CreateNewStep(rightPos);
            rightStep.Name = "并行步骤";
            AddStepToCollections(rightStep);
            System.Diagnostics.Debug.WriteLine($"[InsertParallelBranchAfterTransition] 创建右分支步骤: {rightStep.Id}");

            // 使用右侧连接点创建连接（索引0对应右侧双线连接点）
            System.Diagnostics.Debug.WriteLine($"[InsertParallelBranchAfterTransition] 创建右侧连接: {branch.Id} -> {rightStep.Id}, 源索引=0, 目标索引=0");
            AddConnection(branch.Id, rightStep.Id, 0, 0);
        }
        else
        {
            // 后续分支：只在右侧创建步骤元素（优化的自动步骤生成逻辑）
            Point rightPos = CalculateUnifiedParallelStepPosition(branchPosition, branch.Size, false);
            SFCStepModel rightStep = CreateNewStep(rightPos);
            rightStep.Name = "并行步骤";
            AddStepToCollections(rightStep);
            System.Diagnostics.Debug.WriteLine($"[InsertParallelBranchAfterTransition] 创建右分支步骤: {rightStep.Id}");

            // 使用右侧连接点创建连接
            AddConnection(branch.Id, rightStep.Id, 2, 0);
        }

        // 自动选中新创建的分支
        SelectedElement = BranchViewModels.FirstOrDefault(b => b.Id == branch.Id);

        StatusMessage = $"✅ 已按西门子Graph风格在转换条件[{selectedName}]后插入并行分支";
    }

    /// <summary>
    /// 支持在并行分支后插入新的并行分支
    /// 这是对并行分支的扩展，支持西门子Graph风格的多分支插入
    /// </summary>
    public void InsertParallelBranchFromAnyPart()
    {
        if (SelectedElement is not SFCBranchViewModel branchViewModel)
        {
            StatusMessage = "⚠️ 请先选择一个并行分支";
            return;
        }

        if (branchViewModel.BranchType != SFCBranchType.Parallel)
        {
            StatusMessage = "⚠️ 只有并行分支支持插入兄弟分支";
            return;
        }

        StatusMessage = $"🔄 正在从并行分支插入新的兄弟分支...";

        // 调用并行分支的兄弟插入逻辑
        InsertSiblingParallelBranch(branchViewModel);

        // 更新状态消息
        if (StatusMessage.Contains("✅"))
        {
            StatusMessage = $"✅ 已从并行分支成功插入新的兄弟分支";
        }
    }

    /// <summary>
    /// 插入并行分支的兄弟分支（优化版本）
    /// </summary>
    private void InsertSiblingParallelBranch(SFCBranchViewModel currentBranch)
    {
        if (currentBranch.BranchType != SFCBranchType.Parallel)
        {
            StatusMessage = "⚠️ 只有并行分支支持插入兄弟分支";
            return;
        }

        // 1. 找到分支链的起始分支
        var startBranch = Branches.FirstOrDefault(b => b.Id == currentBranch.Id);
        if (startBranch == null)
        {
            StatusMessage = "⚠️ 无法找到当前分支";
            return;
        }

        // 2. 使用统一的位置计算逻辑
        Point branchPosition = CalculateUnifiedParallelBranchPosition(currentBranch);
        System.Diagnostics.Debug.WriteLine($"[InsertSiblingParallelBranch] 计算的分支位置: {branchPosition}");

        // 3. 创建新分支实例（后续分支，ViewType为Subsequent）
        var newBranch = new SFCBranchModel
        {
            Id = Guid.NewGuid().ToString(),
            Name = $"并行分支_{Branches.Count + 1}",
            ViewType = BranchViewType.Subsequent, // 后续样式，隐藏左侧连接元素
            Position = branchPosition,
            Size = startBranch.Size,
            BranchType = SFCBranchType.Parallel,
            IsConvergence = startBranch.IsConvergence
        };

        // 4. 创建对应的ViewModel
        var newBranchViewModel = new SFCBranchViewModel
        {
            Id = newBranch.Id,
            Name = newBranch.Name,
            BranchType = newBranch.BranchType,
            Position = newBranch.Position,
            Size = newBranch.Size,
            IsConvergence = newBranch.IsConvergence,
            ViewType = newBranch.ViewType, // 同步ViewType
            InputStepIds = [..newBranch.InputStepIds],
            OutputStepIds = [..newBranch.OutputStepIds],
            InternalTransitionNumber = newBranch.InternalTransition?.TransitionNumber ?? 0,
            InternalTransitionCondition = newBranch.InternalTransition?.ConditionExpression ?? "TRUE"
        };

        // 设置命令
        newBranchViewModel.SelectCommand = new RelayCommand(() => SelectBranch(newBranchViewModel));
        newBranchViewModel.DeleteCommand = new RelayCommand(() => DeleteBranch(newBranchViewModel));
        newBranchViewModel.EditPropertiesCommand = new RelayCommand(() => EditBranch(newBranchViewModel));
        newBranchViewModel.StartConnectionCommand = new RelayCommand(() => StartConnection(newBranchViewModel));

        // 5. 连接分支链 - 找到分支链的末端并连接新分支
        var rightmostBranch = FindRightmostBranchInChain(startBranch);
        rightmostBranch.NextBranchId = newBranch.Id;

        // 同步更新对应的ViewModel
        var rightmostBranchViewModel = BranchViewModels.FirstOrDefault(vm => vm.Id == rightmostBranch.Id);
        if (rightmostBranchViewModel != null)
        {
            rightmostBranchViewModel.NextBranchId = newBranch.Id;
        }

        // 6. 将新分支添加到集合
        Branches.Add(newBranch);
        BranchViewModels.Add(newBranchViewModel);
        CurrentSFC.Branches.Add(newBranch);

        // 6.5. 创建分支链连接线（水平连接线）- 修复并行分支扩展缺少连接线的问题
        CreateBranchChainConnection(rightmostBranch, newBranch);
        System.Diagnostics.Debug.WriteLine($"[InsertSiblingParallelBranch] 创建并行分支链连接线: {rightmostBranch.Id} -> {newBranch.Id}");

        // 7. 为后续分支只创建右侧步骤（使用统一的坐标计算逻辑）
        // 只在右侧创建步骤元素，不在左侧创建
        Point rightPos = CalculateUnifiedParallelStepPosition(branchPosition, newBranch.Size, false);
        SFCStepModel rightStep = CreateNewStep(rightPos);
        rightStep.Name = "并行步骤";
        AddStepToCollections(rightStep);
        System.Diagnostics.Debug.WriteLine($"[InsertSiblingParallelBranch] 创建右分支步骤: {rightStep.Id}");

        // 使用右侧连接点创建连接
        AddConnection(newBranch.Id, rightStep.Id, 2, 0);

        // 8. 自动选中新创建的分支
        SelectedElement = newBranchViewModel;

        StatusMessage = $"✅ 已在并行分支链末端插入新的并行分支: {newBranch.Name}";
        IsModified = true;
    }

    /// <summary>
    /// 判断是否可以从当前选中的元素插入并行分支
    /// </summary>
    private bool CanInsertParallelBranchFromAnyPart()
    {
        if (SelectedElement == null)
            return false;

        // 如果选中的是并行分支，则可以插入兄弟分支
        if (SelectedElement is SFCBranchViewModel branchViewModel)
        {
            return branchViewModel.BranchType == SFCBranchType.Parallel;
        }

        return false;
    }

    /// <summary>
    /// 统一的并行分支步骤坐标计算方法
    /// </summary>
    /// <param name="branchPosition">分支位置</param>
    /// <param name="branchSize">分支尺寸</param>
    /// <param name="isLeftStep">是否为左侧步骤</param>
    /// <returns>步骤应该放置的位置</returns>
    private Point CalculateUnifiedParallelStepPosition(Point branchPosition, Size branchSize, bool isLeftStep)
    {
        // 使用统一的参数（与InsertParallelBranchAfterTransition保持一致）
        const double UNIFIED_BRANCH_WIDTH = 76;
        const double UNIFIED_VERTICAL_OFFSET = 14;
        const double UNIFIED_LEFT_ADDITIONAL_OFFSET = -71.5;

        double branchCenterX = branchPosition.X + branchSize.Width / 2;

        if (isLeftStep)
        {
            // 左侧步骤位置计算
            return new Point(
                branchCenterX - UNIFIED_BRANCH_WIDTH / 2 + UNIFIED_LEFT_ADDITIONAL_OFFSET,
                branchPosition.Y + UNIFIED_VERTICAL_OFFSET
            );
        }
        else
        {
            // 右侧步骤位置计算
            return new Point(
                branchCenterX + UNIFIED_BRANCH_WIDTH / 2,
                branchPosition.Y + UNIFIED_VERTICAL_OFFSET
            );
        }
    }

    /// <summary>
    /// 并行分支插入位置计算方法（用于从现有分支插入的情况）
    /// </summary>
    /// <param name="referenceElement">参考元素（现有分支）</param>
    /// <returns>新分支应该插入的位置</returns>
    private Point CalculateUnifiedParallelBranchPosition(object referenceElement)
    {
        const double BRANCH_SPACING = 147.5; // 并行分支间距（与InsertParallelBranchAfterTransition保持一致）

        // 参考元素是现有并行分支
        if (referenceElement is SFCBranchViewModel branchViewModel &&
            branchViewModel.BranchType == SFCBranchType.Parallel)
        {
            // 找到分支链的最右端
            var startBranch = Branches.FirstOrDefault(b => b.Id == branchViewModel.Id);
            if (startBranch != null)
            {
                var rightmostBranch = FindRightmostBranchInChain(startBranch);
                return new Point(rightmostBranch.Position.X + BRANCH_SPACING, rightmostBranch.Position.Y);
            }
        }

        // 默认情况：使用动态方法基于参考元素的连接点位置计算
        var referenceConnectPoint = GetActualConnectPointPosition(referenceElement, 0, true); // 获取输出连接点
        if (referenceConnectPoint.HasValue)
        {
            return new Point(referenceConnectPoint.Value.X + BRANCH_SPACING, referenceConnectPoint.Value.Y + 40);
        }
        else
        {
            // 回退到基于元素位置的计算
            Point defaultPosition = GetElementPosition(referenceElement);
            return new Point(defaultPosition.X + BRANCH_SPACING, defaultPosition.Y + 60);
        }
    }

    /// <summary>
    /// 找到并行分支链中最右侧的分支
    /// </summary>
    /// <param name="startBranch">分支链中的任意一个分支</param>
    /// <returns>分支链中最右侧的分支</returns>
    private SFCBranchModel FindRightmostBranchInChain(SFCBranchModel startBranch)
    {
        // 首先找到分支链的起始分支（没有前驱的分支）
        var currentBranch = startBranch;
        var visitedBranches = new HashSet<string>();

        // 向前查找，找到链的起始点
        while (true)
        {
            if (visitedBranches.Contains(currentBranch.Id))
                break; // 防止循环引用

            visitedBranches.Add(currentBranch.Id);

            var previousBranch = Branches.FirstOrDefault(b => b.NextBranchId == currentBranch.Id);
            if (previousBranch != null)
            {
                currentBranch = previousBranch;
            }
            else
            {
                break; // 找到了起始分支
            }
        }

        // 从起始分支开始，向后查找最右侧的分支
        var rightmostBranch = currentBranch;
        visitedBranches.Clear();

        while (currentBranch != null)
        {
            if (visitedBranches.Contains(currentBranch.Id))
                break; // 防止循环引用

            visitedBranches.Add(currentBranch.Id);
            System.Diagnostics.Debug.WriteLine($"[分支链] 检查分支: {currentBranch.Id}, 位置: {currentBranch.Position}");

            // 更新最右侧分支
            if (currentBranch.Position.X > rightmostBranch.Position.X)
            {
                System.Diagnostics.Debug.WriteLine($"[分支链] 更新最右侧: {rightmostBranch.Position.X} -> {currentBranch.Position.X}");
                rightmostBranch = currentBranch;
            }

            // 查找下一个分支
            if (!string.IsNullOrEmpty(currentBranch.NextBranchId))
            {
                var nextBranch = Branches.FirstOrDefault(b => b.Id == currentBranch.NextBranchId);
                if (nextBranch != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[分支链] 找到下一个: {currentBranch.Id} -> {nextBranch.Id}");
                    currentBranch = nextBranch;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[分支链] NextBranchId无效: {currentBranch.NextBranchId}");
                    break;
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[分支链] 到达末端: {currentBranch.Id}");
                break;
            }
        }

        System.Diagnostics.Debug.WriteLine($"[分支链] 最右侧分支位置: {rightmostBranch.Position.X}");
        return rightmostBranch;
    }



    /// <summary>
    /// 创建新步骤
    /// </summary>
    /// <summary>
    /// 标准步骤尺寸常量，确保整个代码中统一使用
    /// 与SFCStepView.xaml中定义的UI尺寸保持一致
    /// </summary>
    private readonly Size STANDARD_STEP_SIZE = new Size(100, 128);

    /// <summary>
    /// 标准转换条件尺寸常量，确保整个代码中统一使用
    /// 与SFCTransitionView.xaml中定义的UI尺寸保持一致
    /// </summary>
    private readonly Size STANDARD_TRANSITION_SIZE = new Size(74, 30);

    /// <summary>
    /// 选择分支位置偏移配置常量
    /// 这些值控制选择分支相对于步骤的位置，不影响选择分支内部结构
    /// </summary>
    private const double SELECTION_BRANCH_OFFSET_X = 29;  // 水平偏移：控制选择分支相对于步骤中心的水平位置；往右：减小
    private const double SELECTION_BRANCH_OFFSET_Y = 71.5;  // 垂直偏移：控制选择分支相对于步骤底部的垂直间距；往下：增大

    /// <summary>
    /// 选择分支的精确尺寸和连接点位置常量（基于XAML定义）
    /// </summary>
    private const double SELECTION_BRANCH_CANVAS_WIDTH = 200;  // Canvas总宽度
    private const double SELECTION_BRANCH_LEFT_PART_WIDTH = 30; // 左侧部分宽度
    private const double SELECTION_BRANCH_RIGHT_PART_WIDTH = 160; // 右侧部分宽度
    private const double SELECTION_BRANCH_RIGHT_PART_LEFT = 40; // 右侧部分Canvas.Left位置

    // 分支横线的关键位置（基于XAML中HorizontalConnectLine的定义）
    private const double HORIZONTAL_LINE_LEFT_OFFSET = -46;     // 横线最左端相对于RightBranchPart的偏移

    // 不同类型分支的横线右端偏移量（解决Initial和Subsequent分支结构差异）
    private const double HORIZONTAL_LINE_RIGHT_OFFSET_INITIAL = 100;      // Initial分支（第1个）的横线右端偏移，统一为紧凑间距
    private const double HORIZONTAL_LINE_RIGHT_OFFSET_SUBSEQUENT = 101;   // Subsequent分支（第2+个）的横线右端偏移

    // 分支间的理想间隙（避免重叠但保持紧密连接）
    private const double BRANCH_CONNECTION_GAP = 0;

    /// <summary>
    /// 计算新元素位置，确保连接点完全重叠
    /// </summary>
    /// <param name="sourceElement">源元素（选中的元素）</param>
    /// <param name="targetElementSize">目标元素的尺寸</param>
    /// <returns>目标元素的精确位置，使其上连接点与源元素下连接点重叠</returns>
    private Point CalculateConnectPointAlignedPosition(object sourceElement, Size targetElementSize)
    {
        var sourcePosition = GetElementPosition(sourceElement);
        var sourceSize = GetElementSize(sourceElement);

        System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedPosition] 源元素位置: {sourcePosition}");
        System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedPosition] 源元素尺寸: {sourceSize}");
        System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedPosition] 目标元素UI尺寸: {targetElementSize}");

        // 根据XAML模板中定义的实际连接点位置计算
        Point targetPosition;

        if (sourceElement is SFCStepViewModel)
        {
            // 步骤下连接点在XAML中的位置：Canvas.Left="45" Canvas.Top="117"
            var stepBottomConnectPointX = sourcePosition.X + 45;
            var stepBottomConnectPointY = sourcePosition.Y + 117;

            System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedPosition] 步骤下连接点: ({stepBottomConnectPointX}, {stepBottomConnectPointY})");

            if (targetElementSize.Width == 74 && targetElementSize.Height == 30) // 转换条件
            {
                // 修改：让转换条件有独立的位置，不强制连接点重叠
                // 转换条件放置在步骤下方，保持一定距离以显示连接线
                targetPosition = new Point(
                    stepBottomConnectPointX - 55.3, // X对齐：保持X轴对齐
                    stepBottomConnectPointY + 40     // Y偏移：在步骤下方40像素，确保连接线可见
                );

                System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedPosition] 修改后的转换条件位置: ({targetPosition.X}, {targetPosition.Y})");
            }
            else
            {
                // 其他元素，使用几何中心对齐
                targetPosition = new Point(
                    stepBottomConnectPointX - targetElementSize.Width / 2 + 1,
                    stepBottomConnectPointY
                );
            }
        }
        else if (sourceElement is SFCTransitionViewModel)
        {
            // 转换条件下连接点在XAML中的位置：Canvas.Left="55.3" Canvas.Top="25"
            var transitionBottomConnectPointX = sourcePosition.X + 55.3;
            var transitionBottomConnectPointY = sourcePosition.Y + 25;

            System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedPosition] 转换条件下连接点: ({transitionBottomConnectPointX}, {transitionBottomConnectPointY})");

            if (targetElementSize.Width == 90 && targetElementSize.Height == 117) // 步骤
            {
                // 步骤上连接点在XAML中的位置：Canvas.Left="45" Canvas.Top="1"
                // 要实现连接点中心重叠：
                // 转换条件输出连接点：(transitionX + 60.3, transitionY + 30)
                // 步骤输入连接点：(stepX + 50, stepY + 6)
                // 重叠条件：transitionX + 60.3 = stepX + 50, transitionY + 30 = stepY + 6
                // 所以：stepX = transitionX + 10.3, stepY = transitionY + 24
                targetPosition = new Point(
                    transitionBottomConnectPointX - 45, // X对齐：transitionX + 55.3 - 45 = transitionX + 10.3 ✓
                    transitionBottomConnectPointY - 1   // Y对齐：transitionY + 25 - 1 = transitionY + 24 ✓
                );

                System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedPosition] 修正后的步骤位置: ({targetPosition.X}, {targetPosition.Y})");
            }
            else
            {
                // 其他元素，使用几何中心对齐
                targetPosition = new Point(
                    transitionBottomConnectPointX - targetElementSize.Width / 2,
                    transitionBottomConnectPointY
                );
            }
        }
        else
        {
            // 对于其他类型，使用原有的几何中心逻辑
            var sourceBottomCenterX = sourcePosition.X + sourceSize.Width / 2;
            var sourceBottomY = sourcePosition.Y + sourceSize.Height;

            targetPosition = new Point(
                sourceBottomCenterX - targetElementSize.Width / 2,
                sourceBottomY
            );
        }

        System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedPosition] 计算结果: {targetPosition}");

        return targetPosition;
    }

    /// <summary>
    /// 获取精确的连接点位置（优先使用Canvas的FindConnectPointInElement，回退到计算方法）
    /// </summary>
    /// <param name="element">元素对象</param>
    /// <param name="elementPosition">元素位置</param>
    /// <param name="connectPointIndex">连接点索引</param>
    /// <param name="isOutputPoint">是否为输出连接点</param>
    /// <returns>连接点的精确位置</returns>
    public Point GetPreciseConnectPointPosition(object element, Point elementPosition, int connectPointIndex, bool isOutputPoint)
    {
        // 尝试通过Canvas获取精确连接点位置
        var precisePosition = TryGetConnectPointFromCanvas(element, connectPointIndex, isOutputPoint);
        if (precisePosition.HasValue)
        {
            System.Diagnostics.Debug.WriteLine($"[GetPreciseConnectPointPosition] ✅ 使用Canvas精确位置: {precisePosition.Value}");
            return precisePosition.Value;
        }

        // 回退到计算方法
        System.Diagnostics.Debug.WriteLine($"[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法");
        return CalculateElementConnectPoint(element, elementPosition, isOutputPoint, connectPointIndex);
    }

    /// <summary>
    /// 尝试从Canvas获取连接点的精确位置
    /// </summary>
    private Point? TryGetConnectPointFromCanvas(object element, int connectPointIndex, bool isOutputPoint)
    {
        // 这里需要访问Canvas实例，但ViewModel不应该直接访问View
        // 作为临时解决方案，我们可以通过事件或者依赖注入来实现
        // 现在先返回null，让系统回退到计算方法
        return null;
    }

    /// <summary>
    /// 计算元素的精确连接点位置
    /// </summary>
    /// <param name="element">元素对象</param>
    /// <param name="elementPosition">元素位置</param>
    /// <param name="isOutputPoint">是否为输出连接点（true=输出，false=输入）</param>
    /// <param name="connectPointIndex">连接点索引，用于区分多个连接点</param>
    /// <returns>连接点的精确位置</returns>
    public Point CalculateElementConnectPoint(object element, Point elementPosition, bool isOutputPoint, int connectPointIndex = 0)
    {
        // 支持ViewModel和Model类型
        if (element is SFCStepViewModel || element is SFCStepModel)
        {
            if (isOutputPoint)
            {
                // 步骤的下连接点（输出）：Canvas.Left="45" Canvas.Top="117"
                // 连接点控件尺寸10x10，中心偏移+5
                return new Point(elementPosition.X + 45 + 5, elementPosition.Y + 117 + 5);
            }
            else
            {
                // 步骤的上连接点（输入）：Canvas.Left="45" Canvas.Top="1"（修正：XAML中实际是1，不是-5）
                // 连接点控件尺寸10x10，中心偏移+5
                return new Point(elementPosition.X + 45 + 5, elementPosition.Y + 1 + 5);
            }
        }
        else if (element is SFCTransitionViewModel || element is SFCTransitionModel)
        {
            if (isOutputPoint)
            {
                // 转换条件的下连接点（输出）：Canvas.Left="55.3" Canvas.Top="25"
                // 连接点控件尺寸10x10，中心偏移+5
                return new Point(elementPosition.X + 55.3 + 5, elementPosition.Y + 25 + 5);
            }
            else
            {
                // 转换条件的上连接点（输入）：Canvas.Left="55.3" Canvas.Top="-5"
                // 连接点控件尺寸10x10，中心偏移+5
                return new Point(elementPosition.X + 55.3 + 5, elementPosition.Y - 5 + 5);
            }
        }
        else if (element is SFCBranchViewModel branchVM)
        {
            // 选择分支的精确连接点计算（基于XAML中的实际定义）
            // 连接点控件尺寸10x10，中心偏移+5

            SFCBranchType branchType = branchVM.BranchType;

            if (branchType == SFCBranchType.Selection)
            {
                // 选择分支的连接点处理，根据索引区分不同连接点
                if (connectPointIndex == 0)
                {
                    // 索引0：左侧上端连接点（输入）：Canvas.Left="16" Canvas.Top="2"
                    // 连接点控件尺寸10x10，中心偏移+5
                    var point = new Point(elementPosition.X + 16 + 13 , elementPosition.Y + 2 + 5);
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 选择分支ViewModel左上连接点(索引0): {point}");
                    return point;
                }
                else if (connectPointIndex == 1)
                {
                    // 索引1：左侧下端连接点（输出）：Canvas.Left="16" Canvas.Top="30"
                    var point = new Point(elementPosition.X + 16 + 13, elementPosition.Y + 30 + 5);
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 选择分支ViewModel左下连接点(索引1): {point}");
                    return point;
                }
                else if (connectPointIndex == 2)
                {
                    // 索引2：右侧上端连接点（输入）：Canvas.Left="162" Canvas.Top="30"
                    var point = new Point(elementPosition.X + 162 + 13, elementPosition.Y + 30 + 5);
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 选择分支ViewModel右上连接点(索引2): {point}");
                    return point;
                }
                else if (connectPointIndex == 3)
                {
                    // 索引3：右侧下端连接点（输出）- 对应XAML中的TopConnectPoint2
                    // TopConnectPoint2位置：Canvas.Left="162" Canvas.Top="61"
                    // 连接点控件设计尺寸16×16，实际圆形10×10，考虑VerticalAlignment="Center"
                    // 尝试使用8像素偏移（16/2）来获得更精确的中心位置
                    var point = new Point(elementPosition.X + 170 + 5, elementPosition.Y + 61 + 5);
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): {point}");
                    return point;
                }
                else
                {
                    // 默认使用右侧下端连接点
                    var point = new Point(elementPosition.X + 162 + 5, elementPosition.Y + 61 + 5);
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 选择分支ViewModel默认连接点(索引{connectPointIndex}): {point}");
                    return point;
                }
            }
            else if (branchType == SFCBranchType.Parallel)
            {
                // 并行分支的连接点处理
                if (connectPointIndex == 0)
                {
                    if (isOutputPoint)
                    {
                        // 索引0：右侧双线连接点（输出）：Canvas.Left="183" Canvas.Top="19.5"
                        var rightPoint = new Point(elementPosition.X + 183 + 5, elementPosition.Y + 19.5 + 5);
                        System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] 🔴 并行分支右侧连接点(索引0): {rightPoint}");
                        return rightPoint;
                    }
                    else
                    {
                        // 索引0：左侧上端连接点（输入）：Canvas.Left="37" Canvas.Top="-9"
                        return new Point(elementPosition.X + 37 + 5, elementPosition.Y - 9 + 5);
                    }
                }
                else if (connectPointIndex == 1)
                {
                    // 索引1：左侧双线连接点（既可输入也可输出）：Canvas.Left="37" Canvas.Top="19.5"
                    var leftPoint = new Point(elementPosition.X + 37 + 5, elementPosition.Y + 19.5 + 5);
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] 🔵 并行分支左侧连接点(索引1): {leftPoint}");
                    return leftPoint;
                }
                else
                {
                    // 其他索引，默认使用右侧连接点
                    return new Point(elementPosition.X + 183 + 5, elementPosition.Y + 19.5 + 5);
                }
            }
            else
            {
                // 其他分支类型使用几何中心
                var elementSize = GetElementSize(element);
                if (isOutputPoint)
                {
                    return new Point(elementPosition.X + elementSize.Width / 2, elementPosition.Y + elementSize.Height);
                }
                else
                {
                    return new Point(elementPosition.X + elementSize.Width / 2, elementPosition.Y);
                }
            }
        }
        else if (element is SFCBranchModel branchModel)
        {
            // 选择分支模型的精确连接点计算（基于XAML中的实际定义）
            // 连接点控件尺寸10x10，中心偏移+5

            SFCBranchType branchType = branchModel.BranchType;

            System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] SFCBranchModel: 类型={branchType}, 位置={elementPosition}, 输出点={isOutputPoint}");

            // 对比调试：同时计算步骤的连接点位置作为参考
            if (!isOutputPoint)
            {
                var stepReferencePoint = new Point(elementPosition.X + 45 + 5, elementPosition.Y + 1 + 5);
                System.Diagnostics.Debug.WriteLine($"[对比调试] 如果是步骤输入点，位置应该是: {stepReferencePoint}");
            }

            if (branchType == SFCBranchType.Selection)
            {
                // 选择分支的连接点处理，根据索引区分不同连接点
                if (connectPointIndex == 0)
                {
                    // 索引0：左侧上端连接点（输入）：Canvas.Left="16" Canvas.Top="2"
                    // 连接点控件尺寸10x10，中心偏移+5
                    var point = new Point(elementPosition.X + 16 + 5, elementPosition.Y + 2 + 5);
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 选择分支左上连接点(索引0): {point}");
                    return point;
                }
                else if (connectPointIndex == 1)
                {
                    // 索引1：左侧下端连接点（输出）：Canvas.Left="16" Canvas.Top="30"
                    // 统一使用与第3585行相同的计算方法
                    var point = new Point(elementPosition.X + 16 + 13, elementPosition.Y + 30 + 5);
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 选择分支左下连接点(索引1): {point}");
                    return point;
                }
                else if (connectPointIndex == 2)
                {
                    // 索引2：右侧上端连接点（输入）：Canvas.Left="162" Canvas.Top="30"
                    var point = new Point(elementPosition.X + 162 + 5, elementPosition.Y + 30 + 5);
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 选择分支右上连接点(索引2): {point}");
                    return point;
                }
                else if (connectPointIndex == 3)
                {
                    // 索引3：右侧下端连接点（输出）- 对应XAML中的TopConnectPoint2
                    // TopConnectPoint2位置：Canvas.Left="162" Canvas.Top="61"
                    // 连接点控件设计尺寸16×16，实际圆形10×10，考虑VerticalAlignment="Center"
                    // 尝试使用8像素偏移（16/2）来获得更精确的中心位置
                    var point = new Point(elementPosition.X + 170 + 5, elementPosition.Y + 61 + 5);
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 选择分支Model右侧下端连接点(索引3): {point}");
                    return point;
                }
                else
                {
                    // 默认使用右侧下端连接点
                    var point = new Point(elementPosition.X + 170 + 5, elementPosition.Y + 61 + 5);
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 选择分支默认连接点(索引{connectPointIndex}): {point}");
                    return point;
                }
            }
            else if (branchType == SFCBranchType.Parallel)
            {
                // 并行分支的连接点处理
                Point connectPoint;
                if (connectPointIndex == 0)
                {
                    if (isOutputPoint)
                    {
                        // 索引0：右侧双线连接点（输出）：Canvas.Left="183" Canvas.Top="19.5"
                        connectPoint = new Point(elementPosition.X + 183 + 5, elementPosition.Y + 19.5 + 5);
                        System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 并行分支右侧连接点(索引{connectPointIndex}): {connectPoint}");
                    }
                    else
                    {
                        // 索引0：左侧上端连接点（输入）：Canvas.Left="37" Canvas.Top="-9"
                        connectPoint = new Point(elementPosition.X + 37 + 5, elementPosition.Y - 9 + 5);
                        System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 并行分支上端输入点(索引{connectPointIndex}): {connectPoint}");
                    }
                }
                else if (connectPointIndex == 1)
                {
                    // 索引1：左侧双线连接点（既可输入也可输出）：Canvas.Left="37" Canvas.Top="19.5"
                    connectPoint = new Point(elementPosition.X + 37 + 5, elementPosition.Y + 19.5 + 5);
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 并行分支左侧连接点(索引{connectPointIndex}): {connectPoint}");
                }
                else
                {
                    // 其他索引，默认使用右侧连接点
                    connectPoint = new Point(elementPosition.X + 183 + 5, elementPosition.Y + 19.5 + 5);
                    System.Diagnostics.Debug.WriteLine($"[CalculateElementConnectPoint] ✅ 并行分支默认右侧连接点(索引{connectPointIndex}): {connectPoint}");
                }
                return connectPoint;
            }
            else
            {
                // 其他分支类型使用几何中心
                var elementSize = GetElementSize(element);
                if (isOutputPoint)
                {
                    return new Point(elementPosition.X + elementSize.Width / 2, elementPosition.Y + elementSize.Height);
                }
                else
                {
                    return new Point(elementPosition.X + elementSize.Width / 2, elementPosition.Y);
                }
            }
        }
        else
        {
            // 对于其他类型，使用几何中心
            var elementSize = GetElementSize(element);
            if (isOutputPoint)
            {
                return new Point(elementPosition.X + elementSize.Width / 2, elementPosition.Y + elementSize.Height);
            }
            else
            {
                return new Point(elementPosition.X + elementSize.Width / 2, elementPosition.Y);
            }
        }
    }

    // 连接点位置（基于XAML中的实际定义）
    // 这些常量与SFCSelectionBranchView.xaml中的Canvas.Left和Canvas.Top值保持一致
    private const double SELECTION_BRANCH_LEFT_TOP_CONNECT_X = 16;    // 左侧上端连接点X坐标
    private const double SELECTION_BRANCH_LEFT_TOP_CONNECT_Y = 2;     // 左侧上端连接点Y坐标
    private const double SELECTION_BRANCH_LEFT_BOTTOM_CONNECT_X = 16; // 左侧下端连接点X坐标
    private const double SELECTION_BRANCH_LEFT_BOTTOM_CONNECT_Y = 30; // 左侧下端连接点Y坐标
    private const double SELECTION_BRANCH_RIGHT_TOP_CONNECT_X = 162;  // 右侧上端连接点X坐标
    private const double SELECTION_BRANCH_RIGHT_TOP_CONNECT_Y = 30;   // 右侧上端连接点Y坐标
    private const double SELECTION_BRANCH_RIGHT_BOTTOM_CONNECT_X = 162; // 右侧下端连接点X坐标
    private const double SELECTION_BRANCH_RIGHT_BOTTOM_CONNECT_Y = 61;  // 右侧下端连接点Y坐标
    
    /// <summary>
    /// 获取下一个可用的步骤编号
    /// 智能编号分配算法：优先填补编号空缺，确保编号唯一性和连续性
    /// </summary>
    private int GetNextStepNumber()
    {
        // 收集所有现有的步骤编号
        var existingNumbers = new HashSet<int>();

        // 收集所有步骤的编号
        foreach (var step in Steps)
        {
            if (step.StepNumber > 0)
            {
                existingNumbers.Add(step.StepNumber);
            }
        }

        // 如果没有现有编号，从1开始
        if (existingNumbers.Count == 0)
        {
            return 1;
        }

        // 将编号排序，查找第一个缺失的编号
        var sortedNumbers = existingNumbers.OrderBy(n => n).ToList();

        // 检查从1开始是否有缺失的编号
        for (int i = 1; i <= sortedNumbers.Max(); i++)
        {
            if (!existingNumbers.Contains(i))
            {
                return i; // 返回第一个缺失的编号
            }
        }

        // 如果没有缺失编号，返回最大编号+1
        return sortedNumbers.Max() + 1;
    }

    /// <summary>
    /// 创建新步骤
    /// </summary>
    private SFCStepModel CreateNewStep(Point position)
    {
        int stepNumber = GetNextStepNumber();
        return new SFCStepModel
        {
            Id = Guid.NewGuid().ToString(),
            Name = $"步骤{stepNumber}",
            StepNumber = stepNumber,
            Position = position,
            Size = STANDARD_STEP_SIZE
        };
    }

    /// <summary>
    /// 获取下一个可用的转换条件编号
    /// 智能编号分配算法：优先填补编号空缺，确保编号唯一性和连续性
    /// </summary>
    private int GetNextTransitionNumber()
    {
        // 收集所有现有的转换条件编号
        var existingNumbers = new HashSet<int>();

        // 收集独立转换条件的编号
        foreach (var transition in Transitions)
        {
            if (transition.TransitionNumber > 0)
            {
                existingNumbers.Add(transition.TransitionNumber);
            }
        }

        // 收集选择分支内部转换条件的编号
        foreach (var branch in Branches)
        {
            if (branch.BranchType == SFCBranchType.Selection &&
                branch.InternalTransition != null &&
                branch.InternalTransition.TransitionNumber > 0)
            {
                existingNumbers.Add(branch.InternalTransition.TransitionNumber);
            }
        }

        // 如果没有现有编号，从1开始
        if (existingNumbers.Count == 0)
        {
            return 1;
        }

        // 将编号排序，查找第一个缺失的编号
        var sortedNumbers = existingNumbers.OrderBy(n => n).ToList();

        // 检查从1开始是否有缺失的编号
        for (int i = 1; i <= sortedNumbers.Max(); i++)
        {
            if (!existingNumbers.Contains(i))
            {
                return i; // 返回第一个缺失的编号
            }
        }

        // 如果没有缺失编号，返回最大编号+1
        return sortedNumbers.Max() + 1;
    }

    /// <summary>
    /// 创建新转换条件
    /// </summary>
    private SFCTransitionModel CreateNewTransition(Point position)
    {
        int transitionNumber = GetNextTransitionNumber();
        return new SFCTransitionModel
        {
            Id = Guid.NewGuid().ToString(),
            Name = $"转换{transitionNumber}",
            TransitionNumber = transitionNumber,
            Position = position,
            Size = STANDARD_TRANSITION_SIZE,
            ConditionExpression = "TRUE"
        };
    }

    /// <summary>
    /// 创建新分支
    /// </summary>
    private SFCBranchModel CreateNewBranch(Point position, SFCBranchType branchType)
    {
        int branchNumber = Branches.Count + 1;
        var branch = new SFCBranchModel
        {
            Id = Guid.NewGuid().ToString(),
            Name = branchType == SFCBranchType.Selection ? $"选择分支{branchNumber}" : $"并行分支{branchNumber}",
            BranchType = branchType,
            Position = position,
            Size = new Size(200, 25)
        };

        // 为选择分支创建内部转换条件
        if (branchType == SFCBranchType.Selection)
        {
            int transitionNumber = GetNextTransitionNumber();
            branch.InternalTransition = new SFCTransitionModel
            {
                Id = Guid.NewGuid().ToString(),
                Name = $"转换{transitionNumber}",
                TransitionNumber = transitionNumber,
                Position = position, // 位置相对于分支
                Size = new Size(60, 8),
                ConditionExpression = "TRUE"
            };
        }

        return branch;
    }

    /// <summary>
    /// 获取元素位置
    /// </summary>
    private Point GetElementPosition(object element)
    {
        if (element is SFCStepViewModel stepVM)
            return stepVM.Position;
        else if (element is SFCTransitionViewModel transitionVM)
            return transitionVM.Position;
        else if (element is SFCBranchViewModel branchVM)
            return branchVM.Position;
        else if (element is SFCStepModel step)
            return step.Position;
        else if (element is SFCTransitionModel transition)
            return transition.Position;
        else if (element is SFCBranchModel branch)
            return branch.Position;

        return new Point(0, 0);
    }

    /// <summary>
    /// 从ViewModel集合中获取元素位置（优先使用ViewModel的位置，确保与UI一致）
    /// </summary>
    private Point? GetElementPositionFromViewModel(string elementId)
    {
        // 优先查找ViewModel，因为它们的位置与UI显示一致
        var stepVM = StepViewModels.FirstOrDefault(s => s.Id == elementId);
        if (stepVM != null)
            return stepVM.Position;

        var transitionVM = TransitionViewModels.FirstOrDefault(t => t.Id == elementId);
        if (transitionVM != null)
            return transitionVM.Position;

        var branchVM = BranchViewModels.FirstOrDefault(b => b.Id == elementId);
        if (branchVM != null)
            return branchVM.Position;

        return null; // 没有找到对应的ViewModel
    }

    /// <summary>
    /// 在选中元素后插入分支
    /// </summary>
    public void InsertBranchAfterSelected()
    {
        if (SelectedElement == null)
        {
            MessageBox.Show("请先选择一个元素", "操作提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        // 获取选中元素的位置和类型
        Point position = GetElementPosition(SelectedElement);
        SFCElementType elementType = GetElementType(SelectedElement);
        
        // 使用动态方法计算新分支的位置
        Size branchSize = new Size(200, 80); // 选择分支标准尺寸
        Point newPosition = CalculateConnectPointAlignedPositionDynamic(SelectedElement, branchSize);
        
        // 创建新分支
        SFCBranchModel branch = CreateNewBranch(newPosition, SFCBranchType.Selection);
        
        // 根据选中元素类型决定连接方式
        switch (elementType)
        {
            case SFCElementType.Step:
                // 步骤后必须先插入转换条件
                Point transitionPosition = CalculateConnectPointAlignedPositionDynamic(SelectedElement, STANDARD_TRANSITION_SIZE);
                SFCTransitionModel transition = CreateNewTransition(transitionPosition);
                AddTransitionToCollections(transition);
                
                // 连接：选中步骤 -> 转换条件 -> 选择分支
                AddConnection(GetElementId(SelectedElement), transition.Id, 0, 0);
                AddConnection(transition.Id, branch.Id, 0, 0);
                break;
                
            case SFCElementType.Transition:
                // 转换条件后直接连接选择分支
                AddConnection(GetElementId(SelectedElement), branch.Id, 0, 0);
                break;
                
            case SFCElementType.Branch:
                // 分支后不能直接连接分支，需要插入步骤和转换
                Point stepPosition = CalculateConnectPointAlignedPositionDynamic(SelectedElement, STANDARD_STEP_SIZE);
                SFCStepModel step = CreateNewStep(stepPosition);
                AddStepToCollections(step);

                Point branchTransitionPosition = CalculateConnectPointAlignedPositionDynamic(step, STANDARD_TRANSITION_SIZE);
                SFCTransitionModel branchTransition = CreateNewTransition(branchTransitionPosition);
                AddTransitionToCollections(branchTransition);
                
                // 连接：选中分支 -> 步骤 -> 转换条件 -> 新分支
                AddConnection(GetElementId(SelectedElement), step.Id, 0, 0);
                AddConnection(step.Id, branchTransition.Id, 0, 0);
                AddConnection(branchTransition.Id, branch.Id, 0, 0);
                break;
        }
        
        // 添加分支到集合
        AddBranchToCollections(branch);
        
        // 创建分支的左右路径
        // 左路径 - 使用动态方法基于分支左侧连接点位置计算
        var leftConnectPoint = GetActualConnectPointPosition(BranchViewModels.FirstOrDefault(b => b.Id == branch.Id), 1, true); // 索引1：左侧下端连接点
        Point leftPos;
        if (leftConnectPoint.HasValue)
        {
            leftPos = new Point(leftConnectPoint.Value.X - STANDARD_TRANSITION_SIZE.Width / 2, leftConnectPoint.Value.Y + 20);
            System.Diagnostics.Debug.WriteLine($"[InsertBranchAfterSelected] 动态计算左路径位置: 连接点{leftConnectPoint.Value}, 转换条件{leftPos}");
        }
        else
        {
            leftPos = new Point(position.X - 100, newPosition.Y + 60);
            System.Diagnostics.Debug.WriteLine($"[InsertBranchAfterSelected] 回退计算左路径位置: {leftPos}");
        }
        SFCTransitionModel leftTransition = CreateNewTransition(leftPos);
        AddTransitionToCollections(leftTransition);
        AddConnection(branch.Id, leftTransition.Id, 1, 0); // 使用左侧连接点

        // 右路径 - 使用动态方法基于分支右侧连接点位置计算
        var rightConnectPoint = GetActualConnectPointPosition(BranchViewModels.FirstOrDefault(b => b.Id == branch.Id), 2, true); // 索引2：右侧上端连接点
        Point rightPos;
        if (rightConnectPoint.HasValue)
        {
            rightPos = new Point(rightConnectPoint.Value.X - STANDARD_TRANSITION_SIZE.Width / 2, rightConnectPoint.Value.Y + 20);
            System.Diagnostics.Debug.WriteLine($"[InsertBranchAfterSelected] 动态计算右路径位置: 连接点{rightConnectPoint.Value}, 转换条件{rightPos}");
        }
        else
        {
            rightPos = new Point(position.X + 100, newPosition.Y + 60);
            System.Diagnostics.Debug.WriteLine($"[InsertBranchAfterSelected] 回退计算右路径位置: {rightPos}");
        }
        SFCTransitionModel rightTransition = CreateNewTransition(rightPos);
        AddTransitionToCollections(rightTransition);
        AddConnection(branch.Id, rightTransition.Id, 2, 0); // 使用右侧连接点
        
        // 选中新创建的分支
        SelectedElement = BranchViewModels.FirstOrDefault(b => b.Id == branch.Id);
        
        StatusMessage = $"已在选中元素后插入选择分支";
    }

    // 新增：获取元素尺寸
    private Size GetElementSize(object element)
    {
        if (element is SFCStepViewModel stepVM)
            return stepVM.Size;
        else if (element is SFCTransitionViewModel transitionVM)
            return transitionVM.Size;
        else if (element is SFCBranchViewModel branchVM)
            return branchVM.Size;
        else if (element is SFCStepModel step)
            return step.Size;
        else if (element is SFCTransitionModel transition)
            return transition.Size;
        else if (element is SFCBranchModel branch)
            return branch.Size;

        return new Size(0, 0);
    }

    // 新增：自动垂直对齐元素
    private void AlignElementVertically(object sourceElement, object targetElement)
    {
        // 获取源元素和目标元素的位置和尺寸
        Point sourcePosition = GetElementPosition(sourceElement);
        Size sourceSize = GetElementSize(sourceElement);
        Point targetPosition = GetElementPosition(targetElement);
        Size targetSize = GetElementSize(targetElement);
        
        // 计算源元素的中心X坐标
        double sourceCenterX = sourcePosition.X + sourceSize.Width / 2;
        
        // 计算目标元素应该放置的X坐标（保持中心对齐）
        double newTargetX = sourceCenterX - targetSize.Width / 2;
        
        // 更新目标元素的位置
        SetElementPosition(targetElement, new Point(newTargetX, targetPosition.Y));
    }
    
    // 新增：设置元素位置
    private void SetElementPosition(object element, Point position)
    {
        if (element is SFCStepViewModel stepVM)
            stepVM.Position = position;
        else if (element is SFCTransitionViewModel transitionVM)
            transitionVM.Position = position;
        else if (element is SFCBranchViewModel branchVM)
            branchVM.Position = position;
        else if (element is SFCStepModel step)
            step.Position = position;
        else if (element is SFCTransitionModel transition)
            transition.Position = position;
        else if (element is SFCBranchModel branch)
            branch.Position = position;
    }

    /// <summary>
    /// 检查连接是否符合西门子Graph风格规则
    /// </summary>
    private bool IsValidConnectionByGraphStyle(SFCElementType sourceType, SFCElementType targetType)
    {
        // 西门子Graph风格的连接规则
        
        // 规则1：步骤只能连接到转换和选择分支（不能直接连接到其他步骤）
        if (sourceType == SFCElementType.Step)
        {
            if (targetType != SFCElementType.Transition && targetType != SFCElementType.Branch)
            {
                StatusMessage = "西门子Graph规则：步骤只能连接到转换条件或选择分支";
                return false;
            }
        }
        
        // 规则2：转换只能连接到步骤、结束流程、跳转或并行分支
        else if (sourceType == SFCElementType.Transition)
        {
            if (targetType != SFCElementType.Step && targetType != SFCElementType.Branch)
            {
                StatusMessage = "西门子Graph规则：转换条件只能连接到步骤、结束流程、跳转或并行分支";
                return false;
            }
        }
        
        // 规则3：分支特殊规则（由分支类型决定）
        else if (sourceType == SFCElementType.Branch)
        {
            // 分支类型的检查会涉及到具体分支类型，需要进一步细化
            // 这里只做一般性检查
            if (targetType != SFCElementType.Step && targetType != SFCElementType.Transition)
            {
                StatusMessage = "西门子Graph规则：分支只能连接到步骤或转换条件";
                return false;
            }
        }
        
        // 默认允许连接
        return true;
    }

    private void ToggleConvergence(SFCBranchModel branch)
    {
        if (branch == null) return;

        // 切换汇聚状态
        branch.IsConvergence = !branch.IsConvergence;

        // 更新对应的ViewModel
        var branchViewModel = BranchViewModels.FirstOrDefault(bvm => bvm.Id == branch.Id);
        if (branchViewModel != null)
        {
            branchViewModel.IsConvergence = branch.IsConvergence;
        }

        StatusMessage = $"已切换分支 {branch.Name} 的汇聚状态";
        IsModified = true;
    }

    private void InsertSiblingBranch(object? selectedElement)
    {
        // 支持从SFCBranchModel或SFCBranchViewModel开始
        SFCBranchModel? startBranch = null;
        SFCBranchViewModel? startBranchViewModel = null;

        if (selectedElement is SFCBranchModel branchModel)
        {
            startBranch = branchModel;
            startBranchViewModel = BranchViewModels.FirstOrDefault(vm => vm.Id == branchModel.Id);
        }
        else if (selectedElement is SFCBranchViewModel branchViewModel)
        {
            startBranch = Branches.FirstOrDefault(b => b.Id == branchViewModel.Id);
            startBranchViewModel = branchViewModel;
        }

        if (startBranch == null || startBranchViewModel == null) return;

        // 检查是否为选择分支类型
        if (startBranch.BranchType != SFCBranchType.Selection)
        {
            StatusMessage = "⚠️ 只有选择分支支持插入兄弟分支";
            return;
        }

        // 1. 找到分支链的末端
        var currentBranch = startBranch;
        while (currentBranch.NextBranchId != null)
        {
            var nextBranchModel = Branches.FirstOrDefault(b => b.Id == currentBranch.NextBranchId);
            if (nextBranchModel != null)
            {
                currentBranch = nextBranchModel;
            }
            else
            {
                break; // 如果链断裂，则停止
            }
        }

        // 2. 创建新分支实例
        // 按照用户建议的简化逻辑：新分支横线最左端 = 前一个分支横线最右端

        // 正确的理解：让分支的横线端点相连
        // 前一个分支横线右端的绝对位置（根据分支类型选择正确的偏移量）
        double rightOffset = currentBranch.ViewType == BranchViewType.Initial ?
            HORIZONTAL_LINE_RIGHT_OFFSET_INITIAL : HORIZONTAL_LINE_RIGHT_OFFSET_SUBSEQUENT;
        double prevBranchHorizontalLineRightX = currentBranch.Position.X + SELECTION_BRANCH_RIGHT_PART_LEFT + rightOffset;

        // 新分支的位置：让新分支的横线左端与前一个分支的横线右端相连（加小间隙）
        // 新分支横线左端 = 新分支Position.X + SELECTION_BRANCH_RIGHT_PART_LEFT + HORIZONTAL_LINE_LEFT_OFFSET
        // 我们希望：新分支横线左端 = 前一个分支横线右端 + 小间隙
        // 所以：新分支Position.X = (前一个分支横线右端 + 小间隙) - SELECTION_BRANCH_RIGHT_PART_LEFT - HORIZONTAL_LINE_LEFT_OFFSET
        double newBranchX = prevBranchHorizontalLineRightX + BRANCH_CONNECTION_GAP - SELECTION_BRANCH_RIGHT_PART_LEFT - HORIZONTAL_LINE_LEFT_OFFSET;
        double newBranchY = currentBranch.Position.Y; // 保持相同的Y坐标

        // 调试输出：位置计算详情
        System.Diagnostics.Debug.WriteLine($"[InsertSiblingBranch] 横线端点连接位置计算:");
        System.Diagnostics.Debug.WriteLine($"  当前分支位置: X={currentBranch.Position.X}, Y={currentBranch.Position.Y}");
        System.Diagnostics.Debug.WriteLine($"  当前分支ViewType: {currentBranch.ViewType}");
        System.Diagnostics.Debug.WriteLine($"  前一个分支横线右端: {prevBranchHorizontalLineRightX}");
        System.Diagnostics.Debug.WriteLine($"  连接间隙: {BRANCH_CONNECTION_GAP}");
        System.Diagnostics.Debug.WriteLine($"  计算公式: {prevBranchHorizontalLineRightX} + {BRANCH_CONNECTION_GAP} - {SELECTION_BRANCH_RIGHT_PART_LEFT} - ({HORIZONTAL_LINE_LEFT_OFFSET})");
        System.Diagnostics.Debug.WriteLine($"  新分支位置: X={newBranchX}, Y={newBranchY}");
        System.Diagnostics.Debug.WriteLine($"  验证：新分支横线最左端 = {newBranchX + SELECTION_BRANCH_RIGHT_PART_LEFT + HORIZONTAL_LINE_LEFT_OFFSET}");
        System.Diagnostics.Debug.WriteLine($"  常量值: RIGHT_PART_LEFT={SELECTION_BRANCH_RIGHT_PART_LEFT}, LINE_LEFT_OFFSET={HORIZONTAL_LINE_LEFT_OFFSET}, LINE_RIGHT_OFFSET={rightOffset}, GAP={BRANCH_CONNECTION_GAP}");

        // 使用CreateNewBranch方法创建分支，确保选择分支有内部转换条件
        var newBranch = CreateNewBranch(new Point(newBranchX, newBranchY), currentBranch.BranchType);
        newBranch.ViewType = BranchViewType.Subsequent; // 后续样式
        newBranch.Size = currentBranch.Size;
        newBranch.IsConvergence = currentBranch.IsConvergence;

        // 3. 创建对应的ViewModel
        var newBranchViewModel = new SFCBranchViewModel
        {
            Id = newBranch.Id,
            Name = newBranch.Name,
            BranchType = newBranch.BranchType,
            Position = newBranch.Position,
            Size = newBranch.Size,
            IsConvergence = newBranch.IsConvergence,
            ViewType = newBranch.ViewType, // 同步ViewType
            InputStepIds = new List<string>(newBranch.InputStepIds),
            OutputStepIds = new List<string>(newBranch.OutputStepIds),
            InternalTransitionNumber = newBranch.InternalTransition?.TransitionNumber ?? 0,
            InternalTransitionCondition = newBranch.InternalTransition?.ConditionExpression ?? "TRUE"
        };

        // 设置命令
        newBranchViewModel.SelectCommand = new RelayCommand(() => SelectBranch(newBranchViewModel));
        newBranchViewModel.DeleteCommand = new RelayCommand(() => DeleteBranch(newBranchViewModel));
        newBranchViewModel.EditPropertiesCommand = new RelayCommand(() => EditBranch(newBranchViewModel));
        newBranchViewModel.StartConnectionCommand = new RelayCommand(() => StartConnection(newBranchViewModel));

        // 4. 连接分支链
        currentBranch.NextBranchId = newBranch.Id;

        // 同步更新对应的ViewModel
        var currentBranchViewModel = BranchViewModels.FirstOrDefault(vm => vm.Id == currentBranch.Id);
        if (currentBranchViewModel != null)
        {
            currentBranchViewModel.NextBranchId = newBranch.Id;
        }

        // 5. 将新分支添加到集合
        Branches.Add(newBranch);
        BranchViewModels.Add(newBranchViewModel);
        CurrentSFC.Branches.Add(newBranch);

        // 6. 创建分支链连接线（水平连接线）
        CreateBranchChainConnection(currentBranch, newBranch);

        // 7. 验证并调整整个分支链的布局
        ValidateAndAdjustBranchChainLayout(startBranch);

        // 8. 自动选中新创建的分支
        SelectedElement = newBranchViewModel;

        StatusMessage = $"✅ 已在分支链末端插入新的选择分支: {newBranch.Name}";
        IsModified = true;
    }

    /// <summary>
    /// 支持在选择分支的左侧或右侧插入新的选择分支
    /// 这是对InsertSiblingBranch的扩展，支持西门子Graph风格的多分支插入
    /// </summary>
    public void InsertSelectionBranchFromAnyPart()
    {
        if (SelectedElement is not SFCBranchViewModel branchViewModel)
        {
            StatusMessage = "⚠️ 请先选择一个选择分支的左侧或右侧部分";
            return;
        }

        if (branchViewModel.BranchType != SFCBranchType.Selection)
        {
            StatusMessage = "⚠️ 只有选择分支支持插入兄弟分支";
            return;
        }

        // 无论选中左侧还是右侧，都执行相同的插入逻辑
        string selectedPart = branchViewModel.SelectedPart;
        string partDescription = selectedPart == "Left" ? "左侧" : selectedPart == "Right" ? "右侧" : "整体";

        StatusMessage = $"🔄 正在从选择分支{partDescription}插入新的兄弟分支...";

        // 调用现有的插入逻辑
        InsertSiblingBranch(branchViewModel);

        // 更新状态消息
        if (StatusMessage.Contains("✅"))
        {
            StatusMessage = $"✅ 已从选择分支{partDescription}成功插入新的兄弟分支";
        }
    }

    /// <summary>
    /// 检查是否可以从任意分支部分插入新的选择分支
    /// </summary>
    private bool CanInsertSelectionBranchFromAnyPart()
    {
        // 必须选中一个选择分支
        if (SelectedElement is not SFCBranchViewModel branchViewModel)
            return false;

        // 必须是选择分支类型
        if (branchViewModel.BranchType != SFCBranchType.Selection)
            return false;

        // 可以从任意部分插入（左侧、右侧或整体）
        return true;
    }

    /// <summary>
    /// 创建分支链之间的连接线（水平连接线）
    /// 支持选择分支和并行分支的正确连接点索引
    /// </summary>
    private void CreateBranchChainConnection(SFCBranchModel fromBranch, SFCBranchModel toBranch)
    {
        // 根据分支类型确定连接点索引
        int sourceConnectPointIndex;
        int targetConnectPointIndex;

        if (fromBranch.BranchType == SFCBranchType.Selection && toBranch.BranchType == SFCBranchType.Selection)
        {
            // 选择分支链连接
            sourceConnectPointIndex = 2; // 源分支的右侧上端连接点（索引2）

            // 根据目标分支的ViewType选择正确的连接点索引
            var targetBranchViewModel = BranchViewModels.FirstOrDefault(b => b.Id == toBranch.Id);
            if (targetBranchViewModel != null && targetBranchViewModel.ViewType == BranchViewType.Subsequent)
            {
                // 扩展选择分支（Subsequent）应该连接到左侧下端连接点（索引1）
                // 因为左侧上端连接点（索引0）在Subsequent类型中是隐藏的
                targetConnectPointIndex = 1;
                System.Diagnostics.Debug.WriteLine($"[CreateBranchChainConnection] 选择分支扩展：使用左侧下端连接点（索引1）");
            }
            else
            {
                // 初始选择分支（Initial）使用左侧上端连接点（索引0）
                targetConnectPointIndex = 0;
                System.Diagnostics.Debug.WriteLine($"[CreateBranchChainConnection] 选择分支初始：使用左侧上端连接点（索引0）");
            }
        }
        else if (fromBranch.BranchType == SFCBranchType.Parallel && toBranch.BranchType == SFCBranchType.Parallel)
        {
            // 并行分支链连接
            sourceConnectPointIndex = 0; // 源分支的右侧双线连接点（索引0，输出）
            targetConnectPointIndex = 1; // 目标分支的左侧双线连接点（索引1，输入）
            System.Diagnostics.Debug.WriteLine($"[CreateBranchChainConnection] 并行分支链：右侧双线（索引0）-> 左侧双线（索引1）");
        }
        else
        {
            // 混合类型或未知类型，使用默认索引
            sourceConnectPointIndex = 0;
            targetConnectPointIndex = 0;
            System.Diagnostics.Debug.WriteLine($"[CreateBranchChainConnection] 警告：混合或未知分支类型，使用默认索引");
        }

        System.Diagnostics.Debug.WriteLine($"[CreateBranchChainConnection] 分支链连接: {fromBranch.BranchType} 源索引{sourceConnectPointIndex} -> {toBranch.BranchType} 目标索引{targetConnectPointIndex}");

        // 使用AddConnection方法创建连接，确保使用正确的连接点计算
        System.Diagnostics.Debug.WriteLine($"[CreateBranchChainConnection] 创建分支链连接: {fromBranch.Id} -> {toBranch.Id}");
        AddConnection(fromBranch.Id, toBranch.Id, sourceConnectPointIndex, targetConnectPointIndex);
    }

    /// <summary>
    /// 验证并调整分支链的布局，确保所有分支间距一致
    /// </summary>
    private void ValidateAndAdjustBranchChainLayout(SFCBranchModel startBranch)
    {
        var branchChain = new List<SFCBranchModel>();
        var currentBranch = startBranch;

        // 收集整个分支链
        while (currentBranch != null)
        {
            branchChain.Add(currentBranch);
            if (currentBranch.NextBranchId != null)
            {
                currentBranch = Branches.FirstOrDefault(b => b.Id == currentBranch.NextBranchId);
            }
            else
            {
                break;
            }
        }

        // 如果分支链长度小于2，无需调整
        if (branchChain.Count < 2) return;

        System.Diagnostics.Debug.WriteLine($"[ValidateAndAdjustBranchChainLayout] 调整分支链布局，共{branchChain.Count}个分支");

        // 重新计算所有分支的位置，确保无缝连接
        for (int i = 1; i < branchChain.Count; i++)
        {
            var prevBranch = branchChain[i - 1];
            var currentBranchInChain = branchChain[i];

            // 使用横线端点连接的位置计算公式（根据分支类型选择正确的偏移量）
            double prevRightOffset = prevBranch.ViewType == BranchViewType.Initial ?
                HORIZONTAL_LINE_RIGHT_OFFSET_INITIAL : HORIZONTAL_LINE_RIGHT_OFFSET_SUBSEQUENT;
            double prevBranchHorizontalLineRightX = prevBranch.Position.X + SELECTION_BRANCH_RIGHT_PART_LEFT + prevRightOffset;
            double expectedX = prevBranchHorizontalLineRightX + BRANCH_CONNECTION_GAP - SELECTION_BRANCH_RIGHT_PART_LEFT - HORIZONTAL_LINE_LEFT_OFFSET;

            // 如果位置不正确，进行调整
            if (Math.Abs(currentBranchInChain.Position.X - expectedX) > 1.0) // 允许1像素的误差
            {
                System.Diagnostics.Debug.WriteLine($"[ValidateAndAdjustBranchChainLayout] 调整分支{i}: {currentBranchInChain.Position.X} -> {expectedX}");
                System.Diagnostics.Debug.WriteLine($"  前分支横线右端: {prevBranchHorizontalLineRightX}");
                System.Diagnostics.Debug.WriteLine($"  连接间隙: {BRANCH_CONNECTION_GAP}");

                // 更新Model位置
                currentBranchInChain.Position = new Point(expectedX, currentBranchInChain.Position.Y);

                // 同步更新ViewModel位置
                var branchViewModel = BranchViewModels.FirstOrDefault(vm => vm.Id == currentBranchInChain.Id);
                if (branchViewModel != null)
                {
                    branchViewModel.Position = currentBranchInChain.Position;
                }
            }
        }

        System.Diagnostics.Debug.WriteLine($"[ValidateAndAdjustBranchChainLayout] 分支链布局调整完成");
    }

    #region 连接点位置获取方法

    /// <summary>
    /// 获取实际连接点的位置（动态方法）
    /// 这个方法尝试从UI元素中获取连接点的实际位置，与拖拽时的计算方法保持一致
    /// 支持所有SFC元素类型：步骤、转换条件、选择分支、并行分支等
    /// </summary>
    /// <param name="viewModel">元素的ViewModel</param>
    /// <param name="connectPointIndex">连接点索引</param>
    /// <param name="isSource">是否为源连接点（true=输出，false=输入）</param>
    /// <returns>连接点的实际位置，如果无法获取则返回null</returns>
    private Point? GetActualConnectPointPosition(object viewModel, int connectPointIndex, bool isSource = false)
    {
        // 由于在ViewModel中无法直接访问UI层，这里使用统一的计算方法
        // 这确保了与拖拽时动态计算的完全一致性
        try
        {
            Point position;
            Point calculatedPoint;

            switch (viewModel)
            {
                case SFCStepViewModel stepVM:
                    position = stepVM.Position;
                    calculatedPoint = CalculateElementConnectPoint(stepVM, position, isSource, connectPointIndex);
                    System.Diagnostics.Debug.WriteLine($"[GetActualConnectPointPosition] 步骤连接点: 索引{connectPointIndex}, 位置{calculatedPoint}");
                    return calculatedPoint;

                case SFCTransitionViewModel transitionVM:
                    position = transitionVM.Position;
                    calculatedPoint = CalculateElementConnectPoint(transitionVM, position, isSource, connectPointIndex);
                    System.Diagnostics.Debug.WriteLine($"[GetActualConnectPointPosition] 转换条件连接点: 索引{connectPointIndex}, 位置{calculatedPoint}");
                    return calculatedPoint;

                case SFCBranchViewModel branchVM:
                    position = branchVM.Position;
                    calculatedPoint = CalculateElementConnectPoint(branchVM, position, isSource, connectPointIndex);
                    System.Diagnostics.Debug.WriteLine($"[GetActualConnectPointPosition] 分支连接点: 类型{branchVM.BranchType}, 索引{connectPointIndex}, 位置{calculatedPoint}");
                    return calculatedPoint;

                case SFCGraphNodeViewModel graphNodeVM:
                    position = graphNodeVM.Position;
                    // 图形节点暂时使用默认计算
                    calculatedPoint = new Point(position.X + 50, position.Y + 25); // 简化计算
                    System.Diagnostics.Debug.WriteLine($"[GetActualConnectPointPosition] 图形节点连接点: 索引{connectPointIndex}, 位置{calculatedPoint}");
                    return calculatedPoint;

                default:
                    System.Diagnostics.Debug.WriteLine($"[GetActualConnectPointPosition] 不支持的元素类型: {viewModel?.GetType().Name}");
                    return null;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[GetActualConnectPointPosition] 获取连接点位置时发生异常: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 使用动态方法计算连接点对齐的位置
    /// 替代原有的硬编码计算方法，确保与拖拽时的计算一致
    /// </summary>
    /// <param name="sourceElement">源元素</param>
    /// <param name="targetSize">目标元素尺寸</param>
    /// <param name="connectPointIndex">连接点索引，默认为0</param>
    /// <returns>对齐后的位置</returns>
    private Point CalculateConnectPointAlignedPositionDynamic(object sourceElement, Size targetSize, int connectPointIndex = 0)
    {
        try
        {
            // 获取源元素的输出连接点位置（动态方法）
            var sourceConnectPoint = GetActualConnectPointPosition(sourceElement, connectPointIndex, true); // 使用指定的连接点索引
            if (sourceConnectPoint.HasValue)
            {
                // 基于实际连接点位置计算目标元素位置
                double alignedX = sourceConnectPoint.Value.X - targetSize.Width / 2;
                double alignedY = sourceConnectPoint.Value.Y + 20; // 在连接点下方20像素
                var alignedPosition = new Point(alignedX, alignedY);

                System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedPositionDynamic] 动态对齐计算: 连接点{sourceConnectPoint.Value}, 目标位置{alignedPosition}");
                return alignedPosition;
            }
            else
            {
                // 回退到基于元素位置的计算
                var sourcePosition = GetElementPosition(sourceElement);
                var sourceSize = GetElementSize(sourceElement);
                double centerX = sourcePosition.X + sourceSize.Width / 2;
                double alignedX = centerX - targetSize.Width / 2;
                double alignedY = sourcePosition.Y + sourceSize.Height + 20;
                var fallbackPosition = new Point(alignedX, alignedY);

                System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedPositionDynamic] 回退对齐计算: 源位置{sourcePosition}, 目标位置{fallbackPosition}");
                return fallbackPosition;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[CalculateConnectPointAlignedPositionDynamic] 计算对齐位置时发生异常: {ex.Message}");

            // 异常时使用最基本的计算
            var sourcePosition = GetElementPosition(sourceElement);
            return new Point(sourcePosition.X, sourcePosition.Y + 50);
        }
    }

    /// <summary>
    /// 查找选择分支右侧的转换条件ID
    /// </summary>
    /// <param name="branchId">选择分支的ID</param>
    /// <returns>右侧转换条件的ID，如果未找到则返回null</returns>
    private string? FindRightTransitionOfSelectionBranch(string branchId)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"[FindRightTransitionOfSelectionBranch] 开始查找分支{branchId}的右侧转换条件");
            System.Diagnostics.Debug.WriteLine($"[FindRightTransitionOfSelectionBranch] 当前连接总数: {Connections.Count}");
            System.Diagnostics.Debug.WriteLine($"[FindRightTransitionOfSelectionBranch] 当前转换条件总数: {Transitions.Count}");

            // 列出所有相关连接用于调试
            var relatedConnections = Connections.Where(c => c.SourceId == branchId).ToList();
            System.Diagnostics.Debug.WriteLine($"[FindRightTransitionOfSelectionBranch] 分支{branchId}的所有出发连接数: {relatedConnections.Count}");
            foreach (var conn in relatedConnections)
            {
                System.Diagnostics.Debug.WriteLine($"[FindRightTransitionOfSelectionBranch] 连接: {conn.SourceId}[{conn.SourceIndex}] -> {conn.TargetId}[{conn.TargetIndex}]");
            }

            // 查找从选择分支右侧连接点（索引2）出发的连接
            var rightConnection = Connections.FirstOrDefault(c =>
                c.SourceId == branchId && c.SourceIndex == 2);

            if (rightConnection != null)
            {
                System.Diagnostics.Debug.WriteLine($"[FindRightTransitionOfSelectionBranch] 找到右侧连接: {rightConnection.SourceId}[{rightConnection.SourceIndex}] -> {rightConnection.TargetId}[{rightConnection.TargetIndex}]");

                // 检查目标是否为转换条件
                var targetTransition = Transitions.FirstOrDefault(t => t.Id == rightConnection.TargetId);
                if (targetTransition != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[FindRightTransitionOfSelectionBranch] ✅ 找到右侧转换条件: {targetTransition.Id}, 名称: {targetTransition.Name}");
                    return targetTransition.Id;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[FindRightTransitionOfSelectionBranch] ❌ 目标{rightConnection.TargetId}不是转换条件");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[FindRightTransitionOfSelectionBranch] ❌ 未找到从分支{branchId}索引2出发的连接");
            }

            System.Diagnostics.Debug.WriteLine($"[FindRightTransitionOfSelectionBranch] 未找到分支{branchId}的右侧转换条件");
            return null;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[FindRightTransitionOfSelectionBranch] 查找右侧转换条件时发生异常: {ex.Message}");
            return null;
        }
    }

    #endregion
}
