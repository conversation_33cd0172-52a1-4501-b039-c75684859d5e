﻿#pragma checksum "..\..\..\..\Controls\SFCTransitionView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "79452F4B4DDCF86490587FB50071D363927B64D1"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using PC_Control2.Demo.Configuration;
using PC_Control2.Demo.Controls;
using PC_Control2.Demo.Converters;
using PC_Control2.Demo.Models;
using PC_Control2.Demo.ViewModels;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PC_Control2.Demo.Controls {
    
    
    /// <summary>
    /// SFCTransitionView
    /// </summary>
    public partial class SFCTransitionView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 25 "..\..\..\..\Controls\SFCTransitionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas MainCanvas;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Controls\SFCTransitionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle InteractionLayer;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Controls\SFCTransitionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle BottomConnectionLine;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Controls\SFCTransitionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line MainTransitionLine;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Controls\SFCTransitionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line MainTransitionLine2;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\Controls\SFCTransitionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle BottomConnectionLine2;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Controls\SFCTransitionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle BottomConnectionLine3;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Controls\SFCTransitionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal PC_Control2.Demo.Controls.SFCConnectPoint TopConnectPoint1;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\Controls\SFCTransitionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal PC_Control2.Demo.Controls.SFCConnectPoint TopConnectPoint2;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\Controls\SFCTransitionView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border TransitionIdentifier;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PC_Control2.Demo;component/controls/sfctransitionview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\SFCTransitionView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MainCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 2:
            this.InteractionLayer = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 3:
            this.BottomConnectionLine = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 4:
            this.MainTransitionLine = ((System.Windows.Shapes.Line)(target));
            return;
            case 5:
            this.MainTransitionLine2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 6:
            this.BottomConnectionLine2 = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 7:
            this.BottomConnectionLine3 = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 8:
            this.TopConnectPoint1 = ((PC_Control2.Demo.Controls.SFCConnectPoint)(target));
            return;
            case 9:
            this.TopConnectPoint2 = ((PC_Control2.Demo.Controls.SFCConnectPoint)(target));
            return;
            case 10:
            this.TransitionIdentifier = ((System.Windows.Controls.Border)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

