<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:vm="clr-namespace:PC_Control2.Demo.ViewModels"
                    xmlns:controls="clr-namespace:PC_Control2.Demo.Controls"
                    xmlns:config="clr-namespace:PC_Control2.Demo.Configuration">

    <!-- 引用基础样式和通用样式 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="../SFCStyles.xaml"/>
        <ResourceDictionary Source="../SFCCommonStyles.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- 顺控器终止垂直线段样式 -->
    <Style x:Key="SFCTerminatorVerticalLineStyle" TargetType="Rectangle">
        <Setter Property="Width" Value="2"/>
        <Setter Property="Height" Value="20"/>
        <Setter Property="Fill" Value="#FF333333"/>
        <Setter Property="VerticalAlignment" Value="Top"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,10,0,0"/>
    </Style>

    <!-- 顺控器终止水平横线样式 -->
    <Style x:Key="SFCTerminatorHorizontalLineStyle" TargetType="Rectangle">
        <Setter Property="Width" Value="13"/>  <!-- 垂直线段长度(20) × 2/3 ≈ 13 -->
        <Setter Property="Height" Value="3"/>  <!-- 垂直线段宽度(2) × 1.5 = 3 -->
        <Setter Property="Fill" Value="#FF333333"/>
        <Setter Property="VerticalAlignment" Value="Top"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,30,0,0"/>  <!-- 垂直线段下方 -->
    </Style>

    <!-- 顺控器终止连接点样式 -->
    <Style x:Key="SFCTerminatorConnectPointStyle" TargetType="controls:SFCConnectPoint">
        <Setter Property="Width" Value="10"/>
        <Setter Property="Height" Value="10"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Top"/>
        <Setter Property="Margin" Value="0,5,0,0"/>
        <Style.Triggers>
            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <!-- 顺控器终止主容器样式 -->
    <Style x:Key="SFCTerminatorContainerStyle" TargetType="Grid">
        <Setter Property="Width" Value="50"/>
        <Setter Property="Height" Value="50"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Style.Triggers>
            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
            </DataTrigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 顺控器终止内容模板 -->
    <DataTemplate x:Key="SFCTerminatorContentTemplate" DataType="{x:Type vm:SFCStepViewModel}">
        <Grid Style="{StaticResource SFCTerminatorContainerStyle}">
            <!-- 顶部连接点 - 输入连接点 -->
            <controls:SFCConnectPoint x:Name="TopConnectPoint"
                                    ElementId="{Binding Id}"
                                    ElementType="Step"
                                    PointType="Input"
                                    Index="0"
                                    Style="{StaticResource SFCTerminatorConnectPointStyle}"/>

            <!-- 垂直线段 - 从连接点向下延伸 -->
            <Rectangle x:Name="VerticalLine"
                     Style="{StaticResource SFCTerminatorVerticalLineStyle}"/>

            <!-- 水平横线 - 位于垂直线段底部 -->
            <Rectangle x:Name="HorizontalLine"
                     Style="{StaticResource SFCTerminatorHorizontalLineStyle}"/>
        </Grid>
    </DataTemplate>

    <!-- 顺控器终止样式 -->
    <Style x:Key="SFCTerminatorStyle" TargetType="ContentControl">
        <Setter Property="ContentTemplate" Value="{StaticResource SFCTerminatorContentTemplate}"/>
        <Setter Property="Width" Value="50"/>
        <Setter Property="Height" Value="50"/>
        <Setter Property="HorizontalAlignment" Value="Left"/>
        <Setter Property="VerticalAlignment" Value="Top"/>
        <Style.Triggers>
            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
            </DataTrigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
