<UserControl x:Class="PC_Control2.Demo.Views.EnhancedSFCEditorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:controls="clr-namespace:PC_Control2.Demo.Controls"
             xmlns:vm="clr-namespace:PC_Control2.Demo.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             Background="{StaticResource UE_DarkBackground}">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/SFCStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 文本样式 -->
            <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
                <Setter Property="Foreground" Value="#FFB0B0B0"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Margin" Value="8,4"/>
            </Style>

            <!-- 工具栏按钮样式 -->
            <Style x:Key="ToolbarButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="{StaticResource UE_MediumBackground}"/>
                <Setter Property="Foreground" Value="{StaticResource UE_TextPrimary}"/>
                <Setter Property="BorderBrush" Value="{StaticResource UE_Border}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="8,4"/>
                <Setter Property="Margin" Value="2"/>
                <Setter Property="MinWidth" Value="80"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{StaticResource UE_LightBackground}"/>
                        <Setter Property="BorderBrush" Value="{StaticResource UE_BlueSecondary}"/>
                    </Trigger>
                    <Trigger Property="IsPressed" Value="True">
                        <Setter Property="Background" Value="{StaticResource UE_BluePrimary}"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="200"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="{StaticResource UE_LightBackground}" 
                BorderBrush="{StaticResource UE_Border}" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal" Margin="8,4">
                <Button Content="新建" Command="{Binding NewSFCCommand}" Style="{StaticResource ToolbarButtonStyle}"/>
                <Button Content="保存" Command="{Binding SaveSFCCommand}" Style="{StaticResource ToolbarButtonStyle}"/>
                <Button Content="加载" Command="{Binding LoadSFCCommand}" Style="{StaticResource ToolbarButtonStyle}"/>
                
                <Separator Margin="8,0"/>
                
                <!-- 西门子博图风格的插入元素工具栏 -->
                <TextBlock Text="插入元素 (需先选中元素):" Foreground="{StaticResource UE_TextPrimary}" VerticalAlignment="Center" Margin="5,0" />
                
                <!-- 插入步骤 -->
                <Button Command="{Binding InsertStepCommand}" 
                        ToolTip="在选中元素后插入步骤" 
                        Style="{StaticResource ToolbarButtonStyle}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="步骤" />
                    </StackPanel>
                </Button>
                
                <!-- 插入转换条件 -->
                <Button Command="{Binding InsertTransitionCommand}" 
                        ToolTip="在选中元素后插入转换条件" 
                        Style="{StaticResource ToolbarButtonStyle}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="转换条件" />
                    </StackPanel>
                </Button>
                
                <!-- 插入选择分支 -->
                <Button Command="{Binding InsertSelectionBranchCommand}" 
                        ToolTip="在选中元素后插入选择分支" 
                        Style="{StaticResource ToolbarButtonStyle}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="选择分支" />
                    </StackPanel>
                </Button>
                
                <!-- 插入并行分支 -->
                <Button Command="{Binding InsertParallelBranchCommand}" 
                        ToolTip="在选中元素后插入并行分支" 
                        Style="{StaticResource ToolbarButtonStyle}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="并行分支" />
                    </StackPanel>
                </Button>
                
                <!-- 插入顺控器终止 -->
                <Button Command="{Binding InsertEndCommand}"
                        ToolTip="在选中元素后插入顺控器终止"
                        Style="{StaticResource ToolbarButtonStyle}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="顺控器终止" />
                    </StackPanel>
                </Button>
                
                <Separator Margin="8,0"/>
                
                <Button Content="验证" Command="{Binding ValidateSFCCommand}" Style="{StaticResource ToolbarButtonStyle}"/>
                <Button Content="生成代码" Command="{Binding GenerateCodeCommand}" Style="{StaticResource ToolbarButtonStyle}"/>
                <Button Content="导出" Command="{Binding ExportCodeCommand}" Style="{StaticResource ToolbarButtonStyle}"/>
                
                <Separator Margin="8,0"/>
                
                <Button Content="清除选择" Command="{Binding ClearSelectionCommand}" Style="{StaticResource ToolbarButtonStyle}"/>
            </StackPanel>
        </Border>

        <!-- 插入跳转工具栏 -->
        <ToolBar Grid.Row="1" Background="#FF333337" BorderBrush="#FF3F3F46" BorderThickness="0,0,0,1">
            <TextBlock Text="跳转操作:" Foreground="#FFE0E0E0" VerticalAlignment="Center" Margin="5,0" />
            
            <!-- 插入跳转 - 使用下拉菜单 -->
            <ComboBox x:Name="JumpTargetComboBox" Width="200" 
                      Margin="2,0" 
                      Background="#FF1E1E1E" 
                      BorderBrush="#FF3F3F46" 
                      Foreground="#FFE0E0E0"
                      ItemsSource="{Binding Steps}"
                      DisplayMemberPath="Name"
                      SelectedValuePath="Id">
                <ComboBox.Style>
                    <Style TargetType="ComboBox">
                        <Setter Property="IsEnabled" Value="True" />
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Steps.Count}" Value="0">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ComboBox.Style>
            </ComboBox>
            
            <Button Command="{Binding InsertJumpCommand}" 
                    CommandParameter="{Binding SelectedValue, ElementName=JumpTargetComboBox}" 
                    ToolTip="插入跳转到选中步骤" 
                    Padding="5,2" 
                    Margin="2,0" 
                    Background="#FF1E1E1E" 
                    BorderBrush="#FF3F3F46" 
                    Foreground="#FFE0E0E0">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="插入跳转" />
                </StackPanel>
            </Button>
            
            <Separator />
            
            <TextBlock Text="提示: 西门子博图风格SFC编辑器 - 先选中元素，再插入新元素，自动连接" 
                       Foreground="#FFAAAAAA" 
                       VerticalAlignment="Center" 
                       Margin="10,0" 
                       FontStyle="Italic" />
        </ToolBar>

        <!-- 主编辑区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- SFC画布区域 - 采用蓝图编辑器的设计方式 -->
            <Border Grid.Column="0" Background="{StaticResource UE_DarkBackground}"
                    BorderBrush="{StaticResource UE_Border}" BorderThickness="1"
                    Padding="0">

                <!-- SFC画布视图 - 使用ScrollViewer，工作区域左上角对齐编辑器左上角 -->
                <ScrollViewer x:Name="SFCScrollViewer"
                              HorizontalScrollBarVisibility="Auto"
                              VerticalScrollBarVisibility="Auto"
                              Padding="0"
                              Margin="0">

                    <!-- 自定义SFC画布 - 固定3000x3000工作空间，左上角对齐 -->
                    <controls:SFCCanvas x:Name="SFCCanvas"
                                       Width="3000"
                                       Height="3000"
                                       HorizontalAlignment="Left"
                                       VerticalAlignment="Top"
                                       Margin="0"
                                       Steps="{Binding StepViewModels}"
                                       Transitions="{Binding TransitionViewModels}"
                                       Branches="{Binding BranchViewModels}"
                                       Connections="{Binding ConnectionViewModels}">

                    <!-- 右键菜单 -->
                    <controls:SFCCanvas.ContextMenu>
                        <ContextMenu Background="{StaticResource UE_MediumBackground}"
                                     Foreground="{StaticResource UE_TextPrimary}"
                                     BorderBrush="{StaticResource UE_Border}"
                                     BorderThickness="1">
                            <!-- 右键菜单项由SFCCanvas.cs中的AddEmptyAreaContextMenuItems方法动态添加 -->
                        </ContextMenu>
                    </controls:SFCCanvas.ContextMenu>
                    </controls:SFCCanvas>
                </ScrollViewer>
            </Border>

            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" 
                          Background="{StaticResource UE_Border}"/>

            <!-- 属性面板 -->
            <Border Grid.Column="2" Background="{StaticResource UE_MediumBackground}"
                    BorderBrush="{StaticResource UE_Border}" BorderThickness="1,0,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 属性面板标题 -->
                    <Border Grid.Row="0" Background="{StaticResource UE_LightBackground}"
                            BorderBrush="{StaticResource UE_Border}" BorderThickness="0,0,0,1">
                        <TextBlock Text="属性" Style="{StaticResource HeaderTextStyle}" Padding="8"/>
                    </Border>

                    <!-- 属性内容 -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="8">
                            <!-- SFC程序信息 -->
                            <GroupBox Header="SFC程序信息" Margin="0,0,0,8">
                                <StackPanel>
                                    <TextBlock Text="名称:" Margin="0,4,0,2"/>
                                    <TextBox Text="{Binding CurrentSFC.Name}" Margin="0,0,0,4"/>
                                    
                                    <TextBlock Text="描述:" Margin="0,4,0,2"/>
                                    <TextBox Text="{Binding CurrentSFC.Description}" 
                                             AcceptsReturn="True" Height="60" Margin="0,0,0,4"/>
                                    
                                    <TextBlock Text="版本:" Margin="0,4,0,2"/>
                                    <TextBox Text="{Binding CurrentSFC.Version}" Margin="0,0,0,4"/>
                                </StackPanel>
                            </GroupBox>

                            <!-- 选中元素信息 -->
                            <GroupBox Header="选中元素" Margin="0,0,0,8">
                                <StackPanel>
                                    <TextBlock Text="{Binding SelectedElement.Name, StringFormat=名称: {0}}" Margin="0,2"/>
                                    <TextBlock Text="{Binding SelectedElement.Description, StringFormat=描述: {0}}" Margin="0,2"/>
                                    <!-- 根据选中元素类型显示不同属性 -->
                                </StackPanel>
                            </GroupBox>

                            <!-- 统计信息 -->
                            <GroupBox Header="统计信息" Margin="0,0,0,8">
                                <StackPanel>
                                    <TextBlock Text="{Binding Steps.Count, StringFormat=步骤数量: {0}}" Margin="0,2"/>
                                    <TextBlock Text="{Binding Transitions.Count, StringFormat=转换数量: {0}}" Margin="0,2"/>
                                    <TextBlock Text="{Binding Branches.Count, StringFormat=分支数量: {0}}" Margin="0,2"/>
                                    <TextBlock Text="{Binding Connections.Count, StringFormat=连接数量: {0}}" Margin="0,2"/>
                                    <TextBlock Text="{Binding Actions.Count, StringFormat=动作数量: {0}}" Margin="0,2"/>
                                </StackPanel>
                            </GroupBox>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <GridSplitter Grid.Row="2" HorizontalAlignment="Stretch" Height="5"
                      Background="{StaticResource UE_Border}"/>

        <!-- 底部面板 -->
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 验证结果面板 -->
            <Border Grid.Column="0" Background="{StaticResource UE_MediumBackground}"
                    BorderBrush="{StaticResource UE_Border}" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="{StaticResource UE_LightBackground}"
                            BorderBrush="{StaticResource UE_Border}" BorderThickness="0,0,0,1">
                        <TextBlock Text="验证结果" Style="{StaticResource HeaderTextStyle}" Padding="8"/>
                    </Border>

                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="8">
                            <TextBlock Text="{Binding ValidationResult.Summary}"
                                       FontWeight="Bold" Margin="0,0,0,8"/>
                            
                            <!-- 错误列表 -->
                            <ItemsControl ItemsSource="{Binding ValidationResult.Errors}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Message}" Foreground="Red" Margin="0,2"/>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                            
                            <!-- 警告列表 -->
                            <ItemsControl ItemsSource="{Binding ValidationResult.Warnings}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Message}" Foreground="Orange" Margin="0,2"/>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>

            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" 
                          Background="{StaticResource UE_Border}"/>

            <!-- 生成代码面板 -->
            <Border Grid.Column="2" Background="{StaticResource UE_MediumBackground}"
                    BorderBrush="{StaticResource UE_Border}" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="{StaticResource UE_LightBackground}"
                            BorderBrush="{StaticResource UE_Border}" BorderThickness="0,0,0,1">
                        <TextBlock Text="生成的代码" Style="{StaticResource HeaderTextStyle}" Padding="8"/>
                    </Border>

                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                        <TextBox Text="{Binding GeneratedCode}" 
                                 IsReadOnly="True" 
                                 FontFamily="Consolas" 
                                 FontSize="12"
                                 Background="Transparent"
                                 Foreground="{StaticResource UE_TextPrimary}"
                                 BorderThickness="0"
                                 Margin="8"/>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="4" Background="{StaticResource UE_LightBackground}"
                BorderBrush="{StaticResource UE_Border}" BorderThickness="0,1,0,0" Height="25">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" 
                           VerticalAlignment="Center" Margin="8,0"/>
                
                <TextBlock Grid.Column="1" Text="{Binding IsModified, StringFormat=修改状态: {0}}" 
                           VerticalAlignment="Center" Margin="8,0"/>
                
                <TextBlock Grid.Column="2" Text="{Binding CurrentSFC.ModifiedTime, StringFormat=最后修改: {0:HH:mm:ss}}" 
                           VerticalAlignment="Center" Margin="8,0"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
